import { defHttp } from '/@/utils/http/axios';

enum Api {
  GetPublisherInfo = '/shop/publisherRank/queryPublisherRank',
  GetAllGenre = '/shop/getAllGenre',
  GetAllDevice = '/shop/getAllDevice',
  QueryPublisherByName = '/appInfo/queryPublisherByName',
}

/**
 * 查询发行商排名信息
 * @param params 查询参数
 */
export function getPublisherInfoApi(params) {
  return defHttp.post({
    url: Api.GetPublisherInfo,
    data: params,
  });
}

/**
 * 获取所有游戏类别
 */
export function getAllGenreApi() {
  return defHttp.get({
    url: Api.GetAllGenre,
  });
}

/**
 * 获取所有设备
 */
export function getAllDeviceApi() {
  return defHttp.get({
    url: Api.GetAllDevice,
  });
}

/**
 * 根据名称查询发行商
 * @param params 查询参数
 */
export function queryPublisherByNameApi(params) {
  return defHttp.get({
    url: Api.QueryPublisherByName,
    params,
  });
}
