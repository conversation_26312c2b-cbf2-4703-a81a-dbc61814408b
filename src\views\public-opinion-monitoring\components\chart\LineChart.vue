<template>
  <div class="line-chart-wrapper" :style="{ height, width }">
    <!-- 右上角切换按钮 -->
    <button class="show-all-btn" @click="showAll = !showAll">
      {{ showAll ? '去零显示' : '显示全部' }}
    </button>

    <!-- 无数据时 -->
    <div v-if="showEmptyState" class="empty-state">
      <div class="empty-icon">📊</div>
      <div class="empty-text">暂无数据</div>
      <div class="empty-subtext">当前时间段内没有舆情数据</div>
    </div>

    <!-- 有数据时才渲染图表容器 -->
    <div v-else ref="chartRef" class="chart-container"></div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, computed, nextTick } from 'vue';
import { useECharts } from '/@/hooks/web/useECharts';
import type { EChartsOption } from 'echarts';
import dayjs from 'dayjs';

interface SeriesItem { name: string; data: number[]; }
interface ChartData {
  xAxisData: string[];
  seriesData: SeriesItem[];
  legendData: string[];
  xAxisFormatter?: (v: string) => string;
  yAxisName?: string;
}

const props = defineProps<{
  chartData: ChartData;
  colorList?: string[];
  width?: string;
  height?: string;
}>();

const showAll = ref(false);

const displaySeries = computed<SeriesItem[]>(() => {
  if (!showAll.value) {
    return props.chartData.seriesData;
  }
  return props.chartData.legendData.map(name => {
    const found = props.chartData.seriesData.find(s => s.name === name);
    if (found) return found;
    return {
      name,
      data: props.chartData.xAxisData.map(() => 0)
    };
  });
});

const showEmptyState = computed(() => {
  const s = displaySeries.value;
  if (!s.length) return true;
  return s.every(series => series.data.every(v => v === 0));
});

const chartRef = ref<HTMLElement | null>(null);
// 拿到 getInstance 而不是 dispose
const { setOptions, resize, getInstance } = useECharts(chartRef as any);

const getOption = (): EChartsOption => ({
  color: props.colorList,
  tooltip: {
    trigger: 'axis',
    axisPointer: { type: 'cross', label: { backgroundColor: '#6a7985' } }
  },
  legend: {
    data: displaySeries.value.map(s => s.name),
    bottom: 0, type: 'scroll', orient: 'horizontal',
    itemWidth: 12, itemHeight: 12,
    pageIconSize: 12, pageButtonItemGap: 6,
    pageTextStyle: { color: '#999' },
    textStyle: { lineHeight: 20 },
    width: '90%'
  },
  grid: { left: '3%', right: '4%', bottom: '15%', containLabel: true },
  xAxis: {
    type: 'category', boundaryGap: false,
    data: props.chartData.xAxisData,
    axisLabel: {
      formatter: props.chartData.xAxisFormatter
        || ((v: string) => dayjs(v).format('MM-DD'))
    }
  },
  yAxis: { type: 'value', name: props.chartData.yAxisName },
  series: displaySeries.value.map(s => ({
    ...s, type: 'line', smooth: true,
    connectNulls: true, emphasis: { focus: 'series' }
  }))
});

watch(
  [() => props.chartData, showAll] as any,
  async () => {
    // 改：不要 dispose()
    // getInstance()?.dispose();

    // 改为 clear()
    getInstance()?.clear();

    if (!showEmptyState.value) {
      await nextTick();
      setOptions(getOption());
      resize();
    }
  },
  { immediate: true, deep: true }
);
</script>


<style scoped>
.line-chart-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}
.chart-container {
  width: 100%; height: 100%;
}
/* 切换按钮 */
.show-all-btn {
  position: absolute;
  top: 8px; right: 12px;
  z-index: 20;
  padding: 4px 8px;
  background: #fff;
  border: 1px solid #ccc;
  border-radius: 2px;
  cursor: pointer;
}
/* 空状态样式 */
.empty-state {
  position: absolute; top: 50%; left: 50%;
  transform: translate(-50%, -50%);
  text-align: center; color: #999; z-index: 10;
}
.empty-icon { font-size: 48px; margin-bottom: 16px; opacity: 0.6; }
.empty-text { font-size: 16px; font-weight: 500; margin-bottom: 8px; color: #666; }
.empty-subtext { font-size: 14px; color: #999; }
</style>
