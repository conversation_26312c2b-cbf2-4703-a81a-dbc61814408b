<template>


  <a-space direction="vertical" :size="12" style="margin-left: 10px;margin-top: 1%;">
      <a-range-picker :presets="rangePresets" @change="onRangeChange" v-model:value="selectedRange" />
  </a-space>

  <!-- 国家选择 -->
  <a-select
    v-model:value="selectedCountry"
    mode="multiple"
    allowClear
    placeholder="请选择国家/地区"
    style="width: 200px;"
    :max-tag-count="1"
    :max-tag-placeholder="maxTagPlaceholder"
    @change="handleCountryChange"
  >
    <a-select-option value="all" @click="selectAllCountries">选择全部</a-select-option>
    <a-select-option v-for="country in countries" :key="country.value" :value="country.value">
      {{ country.label }}
    </a-select-option>
  </a-select>

  <!-- 平台选择 -->
  <a-select
    v-model:value="selectedPlatform"
    mode="multiple"
    allowClear
    placeholder="请选择平台"
    style="width: 200px;"
    :max-tag-count="1"
    :max-tag-placeholder="maxTagPlaceholder"
    @change="handlePlatformChange"
  >
    <a-select-option value="all" @click="selectAllPlatforms">选择全部</a-select-option>
    <a-select-option v-for="device in devices" :key="device.value" :value="device.value">
      {{ device.label }}
    </a-select-option>
  </a-select>

  <a-row>
  <a-col :span="5"  class="left_card">
      <a-select
        v-model:value="compareMode"
        style="width: 70%;margin-left: 12%;margin-top: 6%;margin-bottom: 5px;"
        :options="[
          { label: '发行商维度', value: 'download' },
          { label: '游戏维度', value: 'income' }
        ]"
        @change="handleModeChange"
      ></a-select>
      <!-- 发行商/游戏卡片区，支持删除 -->
<div class="app_card" v-for="(item,index) in card_data" :key="item.name">
<a-row>
  <a-col :span="3">
    <a-checkbox style="margin-top: 56%;margin-left: 10px;" v-model:checked="item.checked"></a-checkbox>
  </a-col>
  <a-col :span="6">
    <img style="width: 45px;border-radius: 8px 8px 8px 8px;margin-top: 16%;margin-left: 5px;" :src="item.img" alt="">
  </a-col>
  <a-col :span="10">
<div style="font-size: 16px;margin-top: 8%;font-weight: bold;max-width:120px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;" :title="item.name">
{{ item.name.length > 10 ? item.name.slice(0, 10) + '…' : item.name }}
</div>
<div style="font-weight: lighter;max-width:120px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;" :title="item.component_name">
{{ item.component_name.length > 10 ? item.component_name.slice(0, 10) + '…' : item.component_name }}
</div>
  </a-col>
  <a-col :span="3">
    <a-button type="link" @click="handleDelete(index)" style="margin-top: 30%;">删除</a-button>
  </a-col>
</a-row>
</div>
      <div class="app_card" @click="open_add">
        <a-row>
          <a-col :span="8">
            <div style="width: 45px;height: 40px;background-color: #fcfcfc;border-radius: 8px 8px 8px 8px;margin-top: 12%;margin-left: 30px;">
              <img  src="./add.png" alt="" style="margin-top: 14px;margin-left: 14px;;">
            </div>
          </a-col>
          <a-col :span="12" style="margin-top: 7%;margin-left: 10px;">添加{{ webb_name }}</a-col>
        </a-row>
          
      </div>
      <a-button type="primary" style="width: 70%;margin-left: 12%;margin-top: 15px;" @click="data_ok">查询</a-button>
      <!-- <a-button type="primary" style="width: 70%;margin-left: 12%;margin-top: 15px;" danger>删除</a-button> -->
      <div style="font-size: 16px;font-weight: bold;margin-top: 60px;margin-left: 28px;">日期颗粒度</div>
      <a-radio-group style="margin-left: 8%;margin-top: 4%;" v-model:value="data_type">
        <a-radio-button value="day_0">自动（日）</a-radio-button>
        <a-radio-button value="day">日</a-radio-button>
        <a-radio-button value="week">周</a-radio-button>
        <a-radio-button value="mouth">月</a-radio-button>
      </a-radio-group>
      <div style="font-size: 16px;font-weight: bold;margin-top: 19px;margin-left: 28px;">比较指标</div>
      <a-radio-group style="margin-left: 8%;margin-top: 4%;margin-bottom: 50px;" v-model:value="compare_index">
        <a-radio-button value="下载量">下载量</a-radio-button>
        <a-radio-button value="净收入">净收入</a-radio-button>
      </a-radio-group>
  </a-col>
  <a-col class="right_card" :span="18">
    <div style="border-left: 3px solid #29ade6;margin-left: 25px;margin-top: 20px;padding-left: 9px;font-size: 16px;font-weight: bold;">
      查看{{ compare_index === '下载量' ? '下载量' : '净收入' }}
    </div>
    <div id="data_echar" style="width:80%;height: 688px;margin-left: 10%;margin-top: 4%;">
      <div v-if="!hasChartData" class="empty-data-container">
        <i class="empty-icon">📊</i>
        <p>暂无对比数据</p>
        <p class="empty-data-tip">请选择要对比的{{ webb_name }}并点击完成按钮</p>
      </div>
    </div>
  </a-col>
</a-row>
<div class="px-10">
  <Modal @register="register" />
  <GameSelectModal @register="registerGameModal" @ok="onGameAdd" />
  <From_component @register="registerPublisherModal" @ok="onPublisherAdd" />
</div>
</template>

<script  lang="ts" setup>
import { ActionItem, BasicTable } from '/@/components/Table';
import { useListPage } from '/@/hooks/system/useListPage';
import {columns} from './component/From_component.data'
import {ref,onMounted,defineComponent, onUnmounted, h } from 'vue';
import { SearchOutlined, DeleteOutlined } from '@ant-design/icons-vue';
import type { CascaderProps,SelectProps } from 'ant-design-vue';
import { Cascader } from 'ant-design-vue';
import dayjs, { Dayjs } from 'dayjs';
import * as echarts from 'echarts';
import { useModal } from '/@/components/Modal';
import Modal from './addModel.vue';
import { message } from 'ant-design-vue';
import GameSelectModal from './component/GameSelectModal.vue';
import From_component from './addModel.vue';
import { getAllDeviceApi, getDownloadCompareApi, getPublisherInfo3Api, getAllCountryApi } from '/@/api/store-information/competitor-analysis/index';

const webb_name=ref("发行商")
// 时间选择框的便捷选项设置
type RangeValue = [Dayjs, Dayjs];

const rangePresets = ref([
{ label: '当天', value: [dayjs().add(-1, 'd'), dayjs()] },
{ label: '最近三天', value: [dayjs().add(-3, 'd'), dayjs()] },
{ label: '最近一周', value: [dayjs().add(-7, 'd'), dayjs()] },
{ label: '最近一个月', value: [dayjs().add(-1, 'month'), dayjs()] },
{ label: '最近三个月', value: [dayjs().add(-3, 'month'), dayjs()] },
{ label: '最近六个月', value: [dayjs().add(-6, 'month'), dayjs()] },
{ label: '最近一年', value: [dayjs().add(-1, 'year'), dayjs()] },
{ label: '最近两年', value: [dayjs().add(-2, 'year'), dayjs()] },
{ label: '最近三年', value: [dayjs().add(-3, 'year'), dayjs()] },
]);

// 设置默认选中2025年4月19日
const selectedRange = ref<RangeValue>([dayjs('2025-04-19'), dayjs('2025-04-19')]);

const onRangeChange = (dates: RangeValue, dateStrings: string[]) => {
if (dates) {
  console.log('From: ', dates[0], ', to: ', dates[1]);
  console.log('From: ', dateStrings[0], ', to: ', dateStrings[1]);
} else {
  console.log('Clear');
}
};
// 设备选择的数据
  const equipment_data=ref([
    {
      label: 'App Store',
      value: 'apple',
    }, 
    // {
    //   label: 'Google Play',
    //   value: 'google',
    // },
  ])
  // 国家地区的选择
  const select_country = ref<{ value: string; label: string }[]>([]);
  // 选择后的设备数据
  const equipment = ref<string[]>([]);
  // 选择设备多选框
  const options: CascaderProps['options'] = equipment_data.value
  const country_1= ref<SelectProps['options']>(select_country.value);
  const country_data = ref([]);
// 下载量，净收入的结果
  const value2 = ref('下载量');
  const options2 = ref<SelectProps['options']>([
  {
      value: '下载量',
      label: '下载量',
  },
  {
      value: '净收入',
      label: '净收入',
  },
  ]);



// 时间颗粒度
const data_type=ref("day_0");
// 比较指标
const compare_index=ref("下载量");
// 左侧柱状图数据


// 柱状图
let myChart = ref()
  let option = ref({})

  onMounted(async () => {

init(right_data)
})
const init = (data) => {
  // 基于准备好的dom，初始化echarts实例
  myChart.value = echarts.init(document.getElementById('data_echar'));
  // 绘制图表
  option.value = {
    legend: {},
    tooltip: {},
    dataset: {
      source: data
    },
    xAxis: { type: 'category' },
    yAxis: {},
    // Declare several bar series, each will be mapped
    // to a column of dataset.source by default.
    series: [{ type: 'bar' }, { type: 'bar' }, { type: 'bar' }]
  };
  myChart.value.setOption(option.value)
}

// 发行商卡片数据
const card_data = ref([
// 示例数据，实际添加发行商后会 push 进来
// {
//   name: "KONAMI",
//   component_name: "KONAMI集团",
//   img: new URL('./toux.png', import.meta.url).href,
//   checked: false
// }
]);

// 发行商选择回调
const onPublisherAdd = (selectedPublishers: any) => {
if (!selectedPublishers) {
  return;
}
const publishers = Array.isArray(selectedPublishers) ? selectedPublishers : [selectedPublishers];

// 检查是否超过最大数量限制
if (card_data.value.length + publishers.length > 5) {
  message.warning('最多只能添加5个发行商');
  return;
}

// 检查是否有重复发行商
const duplicatePublishers = publishers.filter(publisher => 
  card_data.value.some(existingPublisher => existingPublisher.name === publisher.name)
);

if (duplicatePublishers.length > 0) {
  message.warning(`发行商 ${duplicatePublishers.map(p => p.name).join('、')} 已存在，不能重复添加`);
  return;
}

publishers.forEach((publisher) => {
  if (publisher && publisher.name) {
    card_data.value.push({
      name: publisher.name,
      component_name: publisher.component_name || publisher.name,
      img: publisher.img || new URL('./toux.png', import.meta.url).href,
      checked: true
    });
  }
});
};

// 删除发行商
function deletePublisher(index) {
card_data.value.splice(index, 1);
}

const hasChartData = ref(false);

// 查询数据
const data_ok = async () => {
const metric = compare_index.value;
const granularity = data_type.value === 'day_0' ? 'day' : data_type.value;

// 每次查询前重置图表
if (myChart.value) {
  myChart.value.clear();
  myChart.value.dispose();
  myChart.value = null;
}

if (compareMode.value === 'income') {
  // 游戏维度查询
  const params = {
    startDate: selectedRange.value[0]?.format ? selectedRange.value[0].format('YYYY-MM-DD') : String(selectedRange.value[0]),
    endDate: selectedRange.value[1]?.format ? selectedRange.value[1].format('YYYY-MM-DD') : String(selectedRange.value[1]),
    countryName: selectedCountry.value.length > 0 ? selectedCountry.value : undefined,
    deviceName: selectedPlatform.value.length > 0 ? selectedPlatform.value : undefined,
    publisherId: card_data.value.map(g => g.component_name)
  };
  try {
    const res = await getDownloadCompareApi(params);
    console.log('游戏维度接口返回数据:', res); // 修改日志信息
    
    // 检查返回数据
    if (res && typeof res === 'object' && Object.keys(res).length > 0) {
      // 处理数据为柱状图格式
      const gameNames = Object.keys(res);
      const downloadValues = Object.values(res);
      
      const chartData = {
        title: {
          text: metric === '下载量' ? '游戏下载量对比' : '游戏净收入对比'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params: any) {
            const param = params[0];
            const value = param.value;
            if (metric === '下载量') {
              return `${param.name}<br/>下载量：${value >= 10000 ? (value / 10000).toFixed(1) + 'w' : value}`;
            } else {
              return `${param.name}<br/>净收入：$${value.toLocaleString()}`;
            }
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: gameNames,
          axisLabel: {
            interval: 0,
            rotate: 30,
            formatter: function(value: string) {
              return value.length > 15 ? value.slice(0, 15) + '...' : value;
            }
          }
        },
        yAxis: {
          type: 'value',
          name: metric === '下载量' ? '下载量' : '净收入($)',
          axisLabel: {
            formatter: function(value: number) {
              if (metric === '下载量') {
                return value >= 10000 ? (value / 10000).toFixed(1) + 'w' : value;
              } else {
                return '$' + value.toLocaleString();
              }
            }
          }
        },
        series: [{
          data: downloadValues,
          type: 'bar',
          barWidth: '40%',
          itemStyle: {
            color: '#1890ff'
          },
          label: {
            show: true,
            position: 'top',
            formatter: function(params: any) {
              if (metric === '下载量') {
                return params.value >= 10000 ? (params.value / 10000).toFixed(1) + 'w' : params.value;
              } else {
                return '$' + params.value.toLocaleString();
              }
            }
          }
        }]
      };

      // 初始化图表
      myChart.value = echarts.init(document.getElementById('data_echar'));
      myChart.value.setOption(chartData);
      hasChartData.value = true;
    } else {
      hasChartData.value = false;
      message.warning('暂无数据');
    }
  } catch (error) {
    console.error('获取游戏数据失败:', error);
    message.error('获取游戏数据失败');
    hasChartData.value = false;
  }
} else {
  // 发行商维度查询
  try {
    // 获取选中的发行商名
    const publisherIds = card_data.value.filter(i => i.checked).map(i => i.name);
    const params = {
      publisherId: publisherIds.join(','),
      sortField: granularity,
      sortOrder: metric,
      startTime: selectedRange.value[0]?.format ? selectedRange.value[0].format('YYYY-MM-DD') : String(selectedRange.value[0]),
      endTime: selectedRange.value[1]?.format ? selectedRange.value[1].format('YYYY-MM-DD') : String(selectedRange.value[1]),
      country: selectedCountry.value.length > 0 ? selectedCountry.value : undefined,
      device: selectedPlatform.value.length > 0 ? selectedPlatform.value : undefined,
      pageNo: 1,
      pageSize: 10
    };
    
    const res = await getPublisherInfo3Api(params);
    console.log('发行商接口返回数据:', res);
    
    // 检查返回数据
    if (res && typeof res === 'object' && Object.keys(res).length > 0) {
      // 生成时间标签
      const timeLabels = [];
      const endDate = new Date(selectedRange.value[1]);
      for (let i = 2; i >= 0; i--) {
        const date = new Date(endDate);
        if (granularity === 'day') {
          date.setDate(date.getDate() - i);
          timeLabels.push(date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' }));
        } else if (granularity === 'week') {
          date.setDate(date.getDate() - (i * 7));
          timeLabels.push(`第${Math.ceil((date.getDate() + date.getDay()) / 7)}周`);
        } else if (granularity === 'month') {
          date.setMonth(date.getMonth() - i);
          timeLabels.push(date.toLocaleDateString('zh-CN', { month: '2-digit' }));
        }
      }

      // 处理数据为柱状图格式
      const publisherNames = Object.keys(res);
      const series = publisherNames.map(name => {
        const data = res[name];
        return {
          name: name,
          type: 'bar',
          data: data,
          label: {
            show: true,
            position: 'top',
            formatter: function(params: any) {
              if (metric === '下载量') {
                return params.value >= 10000 ? (params.value / 10000).toFixed(1) + 'w' : params.value;
              } else {
                return '$' + params.value.toLocaleString();
              }
            }
          }
        };
      });
      
      const chartData = {
        title: {
          text: metric === '下载量' ? '发行商下载量对比' : '发行商净收入对比'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: function(params: any) {
            let result = params[0].axisValue + '<br/>';
            params.forEach((param: any) => {
              const value = param.value;
              if (metric === '下载量') {
                result += `${param.seriesName}: ${value >= 10000 ? (value / 10000).toFixed(1) + 'w' : value}<br/>`;
              } else {
                result += `${param.seriesName}: $${value.toLocaleString()}<br/>`;
              }
            });
            return result;
          }
        },
        legend: {
          data: publisherNames,
          top: 30
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: timeLabels,
          axisLabel: {
            interval: 0
          }
        },
        yAxis: {
          type: 'value',
          name: metric === '下载量' ? '下载量' : '净收入($)',
          axisLabel: {
            formatter: function(value: number) {
              if (metric === '下载量') {
                return value >= 10000 ? (value / 10000).toFixed(1) + 'w' : value;
              } else {
                return '$' + value.toLocaleString();
              }
            }
          }
        },
        series: series
      };

      // 初始化图表
      myChart.value = echarts.init(document.getElementById('data_echar'));
      myChart.value.setOption(chartData);
      hasChartData.value = true;
    } else {
      hasChartData.value = false;
      message.warning('暂无数据');
    }
  } catch (error) {
    console.error('获取发行商数据失败:', error);
    message.error('获取发行商数据失败');
    hasChartData.value = false;
  }
}
};

// 监听窗口大小变化，调整图表大小
window.addEventListener('resize', () => {
if (myChart.value) {
  myChart.value.resize();
}
});

// 组件卸载时清理
onUnmounted(() => {
if (myChart.value) {
  myChart.value.dispose();
  myChart.value = null;
}
window.removeEventListener('resize', () => {
  if (myChart.value) {
    myChart.value.resize();
  }
});
});

// Modal 注册回调
const [register, { openModal }] = useModal();

// 对比模式
const compareMode = ref<'download' | 'income'>('download');
const isPublisherMode = ref(true);

// 处理模式切换
const handleModeChange = (value: 'download' | 'income') => {
compareMode.value = value;
switchMode(value);
};

// 切换模式
const switchMode = (mode: 'download' | 'income') => {
// 清空已选择的数据
card_data.value = [];
// 更新维度名称
webb_name.value = mode === 'download' ? '发行商' : '游戏';
// 更新图表标题
if (option.value && option.value.title) {
  option.value.title.text = mode === 'download' ? '发行商下载量对比' : '游戏净收入对比';
  // 更新图表Y轴名称
  if (option.value.yAxis) {
    option.value.yAxis.name = mode === 'download' ? '下载量' : '净收入';
  }
  // 更新图表数据
  if (option.value.series && option.value.series[0]) {
    option.value.series[0].name = mode === 'download' ? '下载量' : '净收入';
  }
  // 更新图表
  myChart.value?.setOption(option.value);
}
};

// 打开添加模态框
const open_add = () => {
if (compareMode.value === 'download') {
  openPublisherModal(true, {
    webb_name: webb_name.value,
    existingPublishers: card_data.value, // 传入已存在的发行商列表
    onOk: onPublisherAdd
  });
} else {
  openGameModal(true, {
    webb_name: webb_name.value,
    onOk: onGameAdd
  });
}
};

// 游戏选择回调
const onGameAdd = (selectedGames: any) => {
if (!selectedGames) {
  return;
}
const games = Array.isArray(selectedGames) ? selectedGames : [selectedGames];

// 检查是否超过最大数量限制
if (card_data.value.length + games.length > 5) {
  message.warning('最多只能添加5个游戏');
  return;
}

// 检查是否有重复游戏
const duplicateGames = games.filter(game => 
  card_data.value.some(existingGame => existingGame.name === game.nameZh)
);

if (duplicateGames.length > 0) {
  message.warning(`游戏 ${duplicateGames.map(g => g.nameZh).join('、')} 已存在，不能重复添加`);
  return;
}

games.forEach((game) => {
  if (game && game.nameZh) {
    card_data.value.push({
      name: game.nameZh,
      component_name: game.gameId || game.nameZh,
      img: game.iconUrl || new URL('./toux.png', import.meta.url).href,
      checked: true
    });
  }
});
};

// 国家和平台数据结构与 download-analysis 保持一致
const selectedCountry = ref<string[]>([]);
const selectedPlatform = ref<string[]>([]);
const selectedGenre = ref<string[]>([]);

const countries = ref<{ value: string; label: string }[]>([]);

const devices = ref<{ value: string; label: string }[]>([]);
const fetchDevices = async () => {
try {
  const res = await getAllDeviceApi();
  devices.value = [
    ...(res || []).map((item: any) => ({
      value: item.value,
      label: item.value === 'apple' ? 'App Store' : item.value,
    }))
  ];
} catch (e) {
  devices.value = [];
}
};

// 获取国家数据
const fetchCountries = async () => {
  try {
    const res = await getAllCountryApi();

    if (res && Array.isArray(res)) {
      countries.value = res
        .filter((item: any) => {
          return item &&
                 typeof item === 'object' &&
                 item.value &&
                 typeof item.value === 'string' &&
                 item.value.trim() !== '' &&
                 item.value !== '5' &&
                 item.value !== '6'; // 过滤掉数字ID
        })
        .map((item: any) => ({
          value: item.value.trim(),
          label: item.value.trim(),
        }));
    } else {
      throw new Error('API返回数据格式不正确');
    }
  } catch (e) {
    // 如果API失败，使用默认数据
    countries.value = [
      { value: '菲律宾', label: '菲律宾' },
      { value: '柬埔寨', label: '柬埔寨' },
      { value: '马来西亚', label: '马来西亚' },
      { value: '泰国', label: '泰国' },
      { value: '文莱', label: '文莱' },
      { value: '新加坡', label: '新加坡' },
      { value: '印度尼西亚', label: '印度尼西亚' },
      { value: '越南', label: '越南' },
      { value: '缅甸', label: '缅甸' },
      { value: '中国台湾', label: '中国台湾' },
      { value: '老挝人民民主共和国', label: '老挝人民民主共和国' },
    ];
  }
};
onMounted(() => {
fetchDevices();
fetchCountries();
});

const checkedList = ref(['下载量', '收入']); // 设置默认选中的值

// 注册游戏选择模态框
const [registerGameModal, { openModal: openGameModal }] = useModal();

// 注册发行商选择模态框
const [registerPublisherModal, { openModal: openPublisherModal }] = useModal();

// 删除游戏
const handleDelete = (index: number) => {
if (compareMode.value === 'income') {
  // 游戏维度时，从列表中删除游戏
  card_data.value.splice(index, 1);
} else {
  // 发行商维度时，从列表中删除发行商
  card_data.value.splice(index, 1);
}
};

// 添加标签溢出处理函数
const maxTagPlaceholder = (omittedValues: any[]) => {
return h('span', { class: 'ellipsis-tag' }, '...');
};

// 添加选择全部和处理函数
const selectAllCountries = () => {
selectedCountry.value = countries.value.map(item => item.value);
};
const handleCountryChange = (value: string[]) => {
if (value.includes('all')) {
  selectedCountry.value = countries.value.map(item => item.value);
}
};

const selectAllPlatforms = () => {
selectedPlatform.value = devices.value.map(item => item.value);
};
const handlePlatformChange = (value: string[]) => {
if (value.includes('all')) {
  selectedPlatform.value = devices.value.map(item => item.value);
}
};

const selectAllGenres = () => {
selectedGenre.value = checkedList.value;
};
const handleGenreChange = (value: string[]) => {
if (value.includes('all')) {
  selectedGenre.value = checkedList.value;
}
};

interface CardItem {
name: string;
component_name: string;
img: string;
checked: boolean;
}

// 修改处理函数
const handleCardClick = (item: CardItem) => {
item.checked = !item.checked;
if (item.checked) {
  card_data.value.push(item);
} else {
  const index = card_data.value.findIndex(card => card.component_name === item.component_name);
  if (index !== -1) {
    card_data.value.splice(index, 1);
  }
}
};
</script>

<style scoped>
.right_card{
background-color:#ffffff;
margin-top: 1%;
margin-left: 20px;
border-radius: 8px 8px 8px 8px;
  box-shadow:
    0px 0px 0px rgba(77, 85, 117, 0.05),
    0px 3px 7px rgba(77, 85, 117, 0.05),
    0px 5px 14px rgba(77, 85, 117, 0.04),
    0px 13px 18px rgba(77, 85, 117, 0.03),
    0px 20px 20px rgba(77, 85, 117, 0.01),
    0px 35px 30px rgba(77, 85, 117, 0);
}
.left_card{
border-radius: 8px 8px 8px 8px;
  box-shadow:
    0px 0px 0px rgba(77, 85, 117, 0.05),
    0px 3px 7px rgba(77, 85, 117, 0.05),
    0px 5px 14px rgba(77, 85, 117, 0.04),
    0px 13px 18px rgba(77, 85, 117, 0.03),
    0px 20px 20px rgba(77, 85, 117, 0.01),
    0px 35px 30px rgba(77, 85, 117, 0);
background-color:#ffffff;
margin-top: 1%;
margin-left: 10px;
/* height: 43vw; */
/* min-height: 775px; */
}
.app_card{
margin-top: 8px;
margin-left: 12%;
background-color: #e2e8eb;
width:70%;
border-radius: 8px 8px 8px 8px;
height: 3vw;
}
.empty-data-container {
display: flex;
flex-direction: column;
align-items: center;
justify-content: center;
height: 100%;
color: #999;
/* background-color: #fafafa; */
border-radius: 8px;

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

p {
  margin: 0;
  font-size: 16px;
}

.empty-data-tip {
  margin-top: 8px;
  font-size: 14px;
  color: #999;
}
}
</style>