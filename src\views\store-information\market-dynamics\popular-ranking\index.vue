<template>
  <div class="whole">
    <div class="header"> 每日排行榜 </div>
    <div class="input">
      <a-row>
        <a-col :span="6">
          <a-range-picker v-model:value="value1" :presets="rangePresets" @change="onRangeChange" style="width: 15vw" />
        </a-col>

        <a-col :span="6">
          <a-select
            mode="multiple"
            allowClear
            v-model:value="selectedCountry"
            placeholder="请选择国家"
            style="width: 15vw"
            @change="handleCountryChange"
            :max-tag-count="2"
            :max-tag-placeholder="maxTagPlaceholder"
          >
            <a-select-option value="all">选择全部</a-select-option>
            <a-select-option v-for="country in countries" :key="country.value" :value="country.value">
              {{ country.label }}
            </a-select-option>
          </a-select>
        </a-col>
        
        <a-col :span="6">
          <a-select
            mode="multiple"
            allowClear
            v-model:value="selectedDevice"
            placeholder="请选择游戏平台"
            style="width: 15vw"
            :max-tag-count="2"
            :max-tag-placeholder="maxTagPlaceholder"
            @change="handleDeviceChange"
          >
            <a-select-option value="all">选择全部</a-select-option>
            <a-select-option v-for="device in devices" :key="device.value" :value="device.value">
              {{ device.label }}
            </a-select-option>
          </a-select>
        </a-col>
      </a-row>

      <a-row style="margin-top: 15px">
        <a-col :span="6">
          <a-select
            mode="multiple"
            allowClear
            v-model:value="selectedGameCategory"
            placeholder="请选择游戏类别"
            style="width: 15vw"
            :max-tag-count="2"
            :max-tag-placeholder="maxTagPlaceholder"
            @change="handleGameCategoryChange"
          >
            <a-select-option value="all">选择全部</a-select-option>
            <a-select-option v-for="game in games" :key="game.value" :value="game.value">
              {{ game.label }}
            </a-select-option>
          </a-select>
        </a-col>

        <a-col :span="6">
          <a-select
            mode="multiple"
            v-model:value="selectedPayment"
            placeholder="请选择付费情况"
            style="width: 15vw"
            allowClear
            :max-tag-count="2"
            :max-tag-placeholder="maxTagPlaceholder"
            @change="handlePaymentChange"
          >
            <a-select-option value="all">选择全部</a-select-option>
            <a-select-option v-for="payment in payments" :key="payment.value" :value="payment.value">
              {{ payment.label }}
            </a-select-option>
          </a-select>
        </a-col>
        <a-col :span="6" />
        <a-col :span="6">
          <a-button @click="fetchData" style="color: #d9d9d9"><SearchOutlined />查询</a-button>
        </a-col>
      </a-row>
    </div>

    <div class="input">
      <div class="header1"> 市场情况 </div>
      <!-- <div class="flex-container">
          <div class="chart-container">
            <div class="market-now" ref="marketNowChartRef"></div>
            <div class="future-prediction" ref="futurePredictionChartRef"></div>
          </div>
          <div class="index-container">
            <div class="index-value">{{ marketIndexValue }}</div>
            <div class="index-label">市场激烈指数</div>
          </div>
        </div> -->

      <a-row>
        <a-col :span="10">
          <div style="width: 30vw; height: 400px" ref="marketNowChartRef"></div>
        </a-col>

        <a-col :span="10">
          <div style="width: 30vw; height: 400px" ref="futurePredictionChartRef"></div>
        </a-col>

        <a-col :span="4" class="index-container">
          <div class="index-value">{{ marketIndexValue }}</div>
          <div class="index-label">市场激烈指数</div>
        </a-col>
      </a-row>
    </div>

    <div class="input">
      <div class="header1"> 各榜单排名 </div>
      <!-- 使用合成后的图表容器 -->
      <div class="user-data-container">
        <div class="combined-chart" ref="combinedChartRef" style="width: 100%; height: 500px"></div>
      </div>

      <div class="download">
        <div class="free">
          <div class="headerl">免费下载</div>
          <div class="inputl">
            <div v-for="(item, index) in freeDownloadList" :key="item.id" class="ranking-container">
              <div class="ranking">{{ index + 1 }}</div>
              <div class="input1-box" @click="goDetail(item)">
                <div class="top-row">
                  <img :src="item.iconUrl" alt="" class="pic" />
                  <div class="info">
                    <div class="info-title">{{ item.nameZh }}</div>
                    <div class="info-company" :title="item.developerName">
                      {{ item.developerName && item.developerName.length > 20 ? item.developerName.slice(0, 20) + '...' : item.developerName }}
                    </div>
                  </div>
                </div>
                <div class="rating-row">
                  <div class="rating-left">
                    <template v-for="starIndex in 5" :key="starIndex">
                      <StarFilled v-if="item.rating >= starIndex" style="color: #ffd700; font-size: 15px" />
                      <StarOutlined v-else style="color: #ffd700; font-size: 15px" />
                    </template>
                    <span class="rating-count">( {{ item.rating }} )</span>
                  </div>
                  <div class="right-info">
                    <div class="download-count"> <DownloadOutlined />{{ item.downloadTimes }}</div>
                    <!-- <div class="price">{{ item.rating }}</div> -->
                  </div>
                </div>
              </div>

            </div>
          </div>
        </div>

        <div class="pay">
          <div class="headerl">付费下载</div>
          <div class="inputl">
            <a-spin :spinning="loading">
              <div v-for="(item, index) in payDownloadList" :key="index" class="ranking-container">
                <div class="input1-box" @click="goDetail(item)">
                  <div class="top-row">
                    <img :src="item.iconUrl" alt="" class="pic" />
                    <div class="info">
                      <div class="info-title">{{ item.nameZh }}</div>
                      <div class="info-company" :title="item.developerName">
                        {{ item.developerName && item.developerName.length > 20 ? item.developerName.slice(0, 20) + '...' : item.developerName }}
                      </div>
                    </div>
                  </div>
                  <div class="rating-row">
                    <div class="rating-left">
                      <template v-for="(star, starIndex) in 5" :key="starIndex">
                        <StarFilled v-if="item.rating >= starIndex + 1" style="color: #ffd700; font-size: 15px" />
                        <StarOutlined v-else style="color: #ffd700; font-size: 15px" />
                      </template>
                      <span class="rating-count">( {{ item.rating }} )</span>
                    </div>
                    <div class="right-info">
                      <div class="download-count"> <DownloadOutlined />{{ item.downloadTimes }}</div>
                      <!-- <div class="price">{{ item.rating }}</div> -->
                    </div>
                  </div>
                </div>
              </div>
            </a-spin>
          </div>
        </div>

        <div class="income">
          <div class="headerl">净收入</div>
          <div class="inputl">
            <div v-for="(item, index) in incomeList" :key="index" class="ranking-container">
              <div class="input1-box" @click="goDetail(item)">
                <div class="top-row">
                  <img :src="item.iconUrl" alt="" class="pic" />
                  <div class="info">
                    <div class="info-title">{{ item.nameZh }}</div>
                    <div class="info-company" :title="item.developerName">
                      {{ item.developerName && item.developerName.length > 20 ? item.developerName.slice(0, 20) + '...' : item.developerName }}
                    </div>
                  </div>
                </div>
                <div class="rating-row">
                  <div class="rating-left">
                    <template v-for="(star, starIndex) in 5" :key="starIndex">
                      <StarFilled v-if="item.rating >= starIndex + 1" style="color: #ffd700; font-size: 15px" />
                      <StarOutlined v-else style="color: #ffd700; font-size: 15px" />
                    </template>
                    <span class="rating-count">( {{ item.rating }} )</span>
                  </div>
                  <div class="right-info">
                    <div class="download-count"> <DownloadOutlined />{{ item.downloadTimes }}</div>
                    <!-- <div class="price">{{ item.rating }}</div> -->
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, h, nextTick, computed } from 'vue';
  import { Space, DatePicker, Select, Button } from 'ant-design-vue';
  import { SearchOutlined, StarFilled, StarOutlined } from '@ant-design/icons-vue';
  import * as echarts from 'echarts';
  import axios from 'axios';
  import { router } from '/@/router';
  import { DownloadOutlined } from '@ant-design/icons-vue';
  import dayjs, { Dayjs } from 'dayjs';
  import { defHttp } from '/@/utils/http/axios';
  const { RangePicker } = DatePicker;
  const { Option } = Select;
  type RangeValue = [Dayjs, Dayjs];
  const freeDownloadList = ref<any[]>([]);
  const payDownloadList = ref<any[]>([]);
  const incomeList = ref<any[]>([]);
  const loading = ref(false);
  const genreRadarData = ref<{ genre: string; income: number; downloadTimes: number }[]>([]);
  import { 
    getIncomeAndDownloadGroupByGenreApi,
    topApi,
    getAllGenreApi,
    getAllDeviceApi,
    currentMarketApi,



   } from '/@/api/store-information/market-dynamics/popular-ranking';

  const rangePresets = ref([
    { label: '当天', value: [dayjs().add(-1, 'd'), dayjs()] },
    { label: '最近三天', value: [dayjs().add(-3, 'd'), dayjs()] },
    { label: '最近一周', value: [dayjs().add(-7, 'd'), dayjs()] },
    { label: '最近一个月', value: [dayjs().add(-1, 'month'), dayjs()] },
    { label: '最近三个月', value: [dayjs().add(-3, 'month'), dayjs()] },
    { label: '最近六个月', value: [dayjs().add(-6, 'month'), dayjs()] },
    { label: '最近一年', value: [dayjs().add(-1, 'year'), dayjs()] },
    { label: '最近两年', value: [dayjs().add(-2, 'year'), dayjs()] },
    { label: '最近三年', value: [dayjs().add(-3, 'year'), dayjs()] },
  ]);
  const onRangeChange = (dates: RangeValue, dateStrings: string[]) => {
    if (dates) {
      console.log('From: ', dates[0], ', to: ', dates[1]);
      console.log('From: ', dateStrings[0], ', to: ', dateStrings[1]);
    } else {
      console.log('Clear');
    }
  };

  const selectedCountry = ref<string[]>([]);
  const countries = [
    { value: '菲律宾', label: '菲律宾' },
    { value: '柬埔寨', label: '柬埔寨' },
    { value: '马来西亚', label: '马来西亚' },
    { value: '泰国', label: '泰国' },
    { value: '文莱', label: '文莱' },
    { value: '新加坡', label: '新加坡' },
    { value: '印度尼西亚', label: '印度尼西亚' },
    { value: '越南', label: '越南' },
    { value: '缅甸', label: '缅甸' },
    { value: '中国台湾', label: '中国台湾' },
    { value: '老挝人民民主共和国', label: '老挝人民民主共和国' },
  ];

  const selectedDevice = ref<string[]>([]);
  const devices = ref<{ value: string; label: string }[]>([]);

  const selectedPayment = ref<string[]>([]);
  const payments = [
    { value: '免费', label: '免费' },
    { value: '付费', label: '付费' },
  ];

  const selectedGameCategory = ref<string[]>([]);
  const games = ref<{ value: string; label: string }[]>([]);

  // 获取输入框游戏类型
  const fetchGameGenres = async () => {
    try {
      const res = await getAllGenreApi();
      console.log('游戏类型：',res)
      games.value = [
        // 新增"选择全部"选项
        ...(res || []).map((item: any) => ({
          value: item.value,
          label: item.value,
        })),
      ];
    } catch (e) {}
  };

  // 获取输入框游戏平台类型
  const fetchAllDevice = async () => {
    try {
      // const res = await defHttp.get({ url: '/shop/getAllDevice' });
      const res = await getAllDeviceApi();
      console.log('获取输入框游戏平台类型：',res)
      // 处理返回值，添加"选择全部"选项
      const deviceOptions = (res || []).map((item: any) => ({
        value: item.value,
        label: item.value,
      }));

      console.log('设备选项：',res)

      // 添加"选择全部"选项
      devices.value = [...deviceOptions];
    } catch (e) {
      devices.value = [
        {
          label: '选择全部',
          value: '',
        },
      ];
    }
  };
  
  // 获取用户数据柱状图和雷达图数据
  const fetchGenreRadarData = async () => {
    try {
      loading.value = true;
      let startTime = '';
      let endTime = '';
      if (value1.value && value1.value.length === 2) {
        startTime = value1.value[0].format('YYYY-MM-DD');
        endTime = value1.value[1].format('YYYY-MM-DD');
      }
      
      // 处理付费情况参数
      let isPaidValue;
      if (selectedPayment.value.length === 0) {
        isPaidValue = 2; // 两个都有
      } else if (selectedPayment.value.length === 1) {
        if (selectedPayment.value[0] === '免费') {
          isPaidValue = 0;
        } else {
          isPaidValue = 1;
        }
      } else {
        isPaidValue = 2; // 两个都有
      }
      // 构建请求体
      const params = {
        countryNames: selectedCountry.value || [],
        platformNames: selectedDevice.value || [],
        gameCategories: selectedGameCategory.value || [],
        isPaid: isPaidValue,
        startTime: startTime,
        endTime: endTime
      }

      // 调用新接口
      const res = await getIncomeAndDownloadGroupByGenreApi(params);
      console.log('用户数据返回数据：',res)
      
      // 转换数据结构保持兼容
      genreRadarData.value = (res || []).map(item => ({
        genre: item.genre,
        income: item.income,
        downloadTimes: item.downloadTimes
      }));
      
      console.log('获取游戏类型数据成功:', genreRadarData.value);
      
      // 刷新合成图表
      if (combinedChartRef.value) {
        const combinedChart = echarts.init(combinedChartRef.value);
        combinedChart.setOption(getCombinedOption(), true);
        combinedChart.resize();
      }
    } catch (e) {
      console.error('获取游戏类型数据失败:', e);
      genreRadarData.value = [];
    }
  };

  // 查询三个榜单数据
  const fetchAllRankings = async () => {
    loading.value = true;
    try {
      // 公共参数
      let startTime = '';
      let endTime = '';
      if (value1.value && value1.value.length === 2) {
        startTime = value1.value[0].format('YYYY-MM-DD');
        endTime = value1.value[1].format('YYYY-MM-DD');
      }

      const baseParams = {
        countryNames: selectedCountry.value || [],
        platformNames: selectedDevice.value || [],
        gameCategories: selectedGameCategory.value || [],
        startTime,
        endTime
      };

      // 并行请求三个榜单
      const [freeRes, paidRes, incomeRes] = await Promise.all([
        topApi({ ...baseParams, sortField: 'top_free' }),
        topApi({ ...baseParams, sortField: 'top_paid' }),
        topApi({ ...baseParams, sortField: 'top_grossing' }),
      ]);

      console.log('三个榜单数据：',freeRes, paidRes, incomeRes)

      // 处理返回数据格式
      freeDownloadList.value = (freeRes || []).map(item => ({
        gameId: item.id,
        iconUrl: item.icon,
        nameZh: item.gameName,
        developerName: item.publisher,
        rating: item.ranking,
        downloadTimes: item.downloads
      }));

      payDownloadList.value = (paidRes || []).map(item => ({
        gameId: item.id,
        iconUrl: item.icon,
        nameZh: item.gameName,
        developerName: item.publisher,
        rating: item.ranking,
        downloadTimes: item.downloads
      }));

      incomeList.value = (incomeRes || []).map(item => ({
        gameId: item.id,
        iconUrl: item.icon,
        nameZh: item.gameName,
        developerName: item.publisher,
        rating: item.ranking,
        downloadTimes: item.downloads
      }));

    } catch (error) {
      console.error('获取榜单数据失败:', error);
      freeDownloadList.value = [];
      payDownloadList.value = [];
      incomeList.value = [];
    } finally {
      loading.value = false;
    }
  };

  // 市场现状和未来预测图表
  const marketNowChartRef = ref<HTMLElement | null>(null);
  const futurePredictionChartRef = ref<HTMLElement | null>(null);

  // 合成后的图表（水平柱状图+新雷达图）
  const combinedChartRef = ref<HTMLElement | null>(null);

  // 市场激烈指数，初始设置死数据
  const marketIndexValue = ref(
    computed(() => {
      const { downloads, income, app_numbers } = marketRadarData.value;
      // 归一化处理，避免数值过大
      const normalizedDownloads = downloads / 1000;
      const normalizedIncome = income / 1000;
      const normalizedApps = app_numbers / 10;

      // 计算市场激烈指数：(下载量 + 收入 + 游戏数量) / 3
      // 使用加权平均，下载量权重0.4，收入权重0.4，游戏数量权重0.2
      const index = (normalizedDownloads * 0.4 + normalizedIncome * 0.4 + normalizedApps * 0.2).toFixed(1);
      return index;
    })
  );

  const marketRadarData = ref({
    downloads: 0,
    income: 0,
    app_numbers: 0,
  });

  // 市场现状雷达图配置函数
  const getMarketNowOption = () => {
    // 单位转换（亿/万）
    const downIn100M = marketRadarData.value.downloads / 100000000;
    const incomeIn100M = marketRadarData.value.income / 100000000;
    const appsIn10K = marketRadarData.value.app_numbers / 10000;
    return {
      title: {
        text: '市场现状',
        textStyle: { fontSize: 20 },
        left: 'center',
        top: '5%',
      },
      tooltip: {
        show: true,
        trigger: 'item',
        formatter: function (params) {
          if (params.value) {
            return (
              params.name + '<br/>' +
               '下载量: ' + marketRadarData.value.downloads.toLocaleString() + '<br/>' + 
               '总收入: ' + marketRadarData.value.income.toLocaleString() + '<br/>' + 
               '游戏总数: ' + marketRadarData.value.app_numbers.toLocaleString()
            );
          }
          return '';
        },
      },
      radar: {
        name: { 
          textStyle: { 
            color: '#808080' 
          } 
        },
        shape: 'polygon',
        center: ['50%', '60%'],
        splitArea: {
          show: true,
          areaStyle: {
            color: ['rgba(230, 245, 230, 0.3)', 'rgba(230, 245, 230, 0.2)', 'rgba(230, 245, 230, 0.1)'],
            opacity: 0.8,
          },
        },
        axisLine: { lineStyle: { color: '#ccc', width: 1 } },
        splitLine: { lineStyle: { color: '#ccc', type: 'dashed' } },
        indicator: [
          { name: `下载量\n(${downIn100M.toFixed(1)}亿)`, max: Math.ceil(downIn100M * 1.2) },
          { name: `总收入\n(${incomeIn100M.toFixed(1)}亿)`, max: Math.ceil(incomeIn100M * 1.2) },
          { name: `游戏总数\n(${appsIn10K.toFixed(1)}万)`, max: Math.ceil(appsIn10K * 1.2) }
        ],
      },
      series: [
        {
          name: '市场数据',
          type: 'radar',
          itemStyle: { color: '#009966', borderColor: '#009966', borderWidth: 2 },
          lineStyle: { color: '#009966', width: 2 },
          areaStyle: { color: 'rgba(0, 153, 102, 0.3)' },

          data: [
            {
              value: [downIn100M, incomeIn100M, appsIn10K],
              name: '现状数据',
            },
          ],
        },
      ],
    };
  };

  // 未来预测雷达图配置函数
  const getFuturePredictionOption = () => {
    // 获取当前市场数据的转换值（用于设置合理的max值）
    const currentDownIn100M = marketRadarData.value.downloads / 100000000;
    const currentIncomeIn100M = marketRadarData.value.income / 100000000;
    const currentAppsIn10K = marketRadarData.value.app_numbers / 10000;

    // 生成 110%~150% 的随机增长
    function randomIncrease(val: number) {
      const percent = Math.random() * 0.4 + 1.1; // 1.1 ~ 1.5
      return Math.round(val * percent);
    }

    // 用当前市场数据生成预测数据
    const downloads = randomIncrease(marketRadarData.value.downloads);
    const income = randomIncrease(marketRadarData.value.income);
    const appNumbers = randomIncrease(marketRadarData.value.app_numbers);

    // 单位转换（亿/万）
    const downIn100M = downloads / 100000000;
    const incomeIn100M = income / 100000000;
    const appsIn10K = appNumbers / 10000;

    return {
      title: {
        text: '未来预测',
        textStyle: { fontSize: 20 },
        left: 'center',
        top: '5%',
      },
      tooltip: {
        show: true,
        trigger: 'item',
        formatter: function (params) {
          if (params.value) {
          return (
            params.name + '<br/>' +
            '下载量: ' + downloads.toLocaleString() + '<br/>' + 
            '总收入: ' + income.toLocaleString() + '<br/>' + 
            '游戏总数: ' + appNumbers.toLocaleString()
          );
        }
        return '';
        },
      },
      radar: {
        name: { 
          textStyle: { 
            color: '#808080' 
          } 
        },
        shape: 'polygon',
        center: ['50%', '60%'],
        splitArea: {
          show: true,
          areaStyle: {
            color: ['rgba(245, 230, 230, 0.3)', 'rgba(245, 230, 230, 0.2)', 'rgba(245, 230, 230, 0.1)'],
            opacity: 0.8,
          },
        },
        axisLine: { lineStyle: { color: '#ccc', width: 1 } },
        splitLine: { lineStyle: { color: '#ccc', type: 'dashed' } },
        indicator: [
          { name: `下载量\n(${downIn100M.toFixed(1)}亿)`, max: Math.ceil(currentDownIn100M * 1.5) },
          { name: `总收入\n(${incomeIn100M.toFixed(1)}亿)`, max: Math.ceil(currentIncomeIn100M * 1.5) },
          { name: `游戏总数\n(${appsIn10K.toFixed(1)}万)`, max: Math.ceil(currentAppsIn10K * 1.5) }
        ],
      },
      series: [
        {
          name: '预测数据',
          type: 'radar',
          itemStyle: { color: '#FF5733', borderColor: '#FF5733', borderWidth: 2 },
          lineStyle: { color: '#FF5733', width: 2 },
          areaStyle: { color: 'rgba(255, 87, 51, 0.3)' },
          data: [
            {
              value: [downIn100M, incomeIn100M, appsIn10K],
              name: '预测数据',
            },
          ],
        },
      ],
    };
  };

  // 用于合成图表的统一颜色变量
  const chartColors = ['#4499FF', '#66CCCC', '#9966FF'];

  // 添加随机数生成函数
  const generateRandomData = () => {
    return {
      age16_24: Math.floor(Math.random() * 100),
      age25_36: Math.floor(Math.random() * 100),
      age36plus: Math.floor(Math.random() * 100),
    };
  };

  // 添加响应式数据
  const ageData = ref(generateRandomData());

  // 合成后的图表配置函数：包含水平柱状图和新的雷达图，共用一个图例
  // 调整了 radar 部分的 center 和 radius，使其与市场现状雷达图的大小相似
  const getCombinedOption = () => {
    // 兜底：无数据时给默认项，避免 ECharts 报错
    if (!genreRadarData.value.length) {
      return {
        title: { 
          text: '用户数据', 
          left: 'center' 
        },
        legend: { 
          data: ['16 - 24', '25 - 36', '36+', '收入', '下载量'], 
          bottom: 10, 
          left: 'center' 
        },
        grid: {
          left: '15%',        // 柱状图区域左侧位置
          top: '15%',         // 柱状图区域顶部位置
          bottom: '25%',      // 柱状图区域底部位置
          width: '25%',       // 柱状图区域宽度
        },
        xAxis: {
          type: 'value',      // 数值轴（水平）
        },
        yAxis: {
          type: 'category',
          data: ['各年龄段用户数量', '各年龄段下载量', '各年龄段付费总额'],
        },
        radar: {
          indicator: [{ name: '无数据', max: 1 }],
          center: ['75%', '50%'],
          radius: '65%',
          name: { textStyle: { color: '#808080' } },
        },
        series: [
          {
            name: '16 - 24',
            type: 'bar',
            barWidth: 40,
            stack: '总量',
            itemStyle: { color: chartColors[0] },
            data: [ageData.value.age16_24, 0, 0],
          },
          {
            name: '25 - 36',
            type: 'bar',
            stack: '总量',
            itemStyle: { color: chartColors[1] },
            data: [ageData.value.age25_36, 0, 0],
          },
          {
            name: '36+',
            type: 'bar',
            stack: '总量',
            itemStyle: { color: chartColors[2] },
            data: [ageData.value.age36plus, 0, 0],
          },
          {
            name: '收入',
            type: 'radar',
            lineStyle: { color: '#FF5733', width: 2 },
            itemStyle: { color: '#FF5733' },
            data: [{ value: [0], name: '收入' }],
          },
          {
            name: '下载量',
            type: 'radar',
            lineStyle: { color: '#4499FF', width: 2 },
            itemStyle: { color: '#4499FF' },
            data: [{ value: [0], name: '下载量' }],
          },
        ],
      };
    }

    // 计算最大值，并添加20%的余量
    const maxIncome = Math.max(...genreRadarData.value.map((i) => i.income));
    const maxDownload = Math.max(...genreRadarData.value.map((i) => i.downloadTimes));
    const maxValue = Math.max(maxIncome, maxDownload, 100) * 1.2;

    const indicators = genreRadarData.value.map((item) => ({
      name: item.genre,
      max: maxValue,
    }));

    // 确保数据不超过最大值
    const incomeData = genreRadarData.value.map((item) => Math.min(item.income, maxValue));
    const downloadData = genreRadarData.value.map((item) => Math.min(item.downloadTimes, maxValue));

    return {
      title: {
        text: '用户数据',
        textStyle: { fontSize: 20 },
        left: 'center',
      },
      legend: {
        data: ['16 - 24', '25 - 36', '36+', '收入', '下载量'],
        bottom: 10,
        left: 'center',
        textStyle: { fontSize: 12 },
      },
      grid: {
        left: '15%',
        top: '15%',
        bottom: '25%',
        width: '25%',
      },
      xAxis: {
        type: 'value',
      },
      yAxis: {
        type: 'category',
        data: ['各年龄段用户数量', '各年龄段下载量', '各年龄段付费总额'],
      },
      radar: {
        indicator: indicators,
        center: ['75%', '50%'],
        radius: '65%',
        name: { textStyle: { color: '#808080' } },
      },
      series: [
        {
          name: '16 - 24',
          type: 'bar',
          barWidth: 40,
          stack: '总量',
          itemStyle: { color: chartColors[0] },
          data: [40, 35, 10],
        },
        {
          name: '25 - 36',
          type: 'bar',
          stack: '总量',
          itemStyle: { color: chartColors[1] },
          data: [30, 30, 30],
        },
        {
          name: '36+',
          type: 'bar',
          stack: '总量',
          itemStyle: { color: chartColors[2] },
          data: [30, 35, 60],
        },
        {
          name: '收入',
          type: 'radar',
          lineStyle: { color: '#FF5733', width: 2 },
          itemStyle: { color: '#FF5733' },
          areaStyle: { color: 'rgba(255, 87, 51, 0.3)' },
          data: [{ value: incomeData, name: '收入' }],
        },
        {
          name: '下载量',
          type: 'radar',
          lineStyle: { color: '#4499FF', width: 2 },
          itemStyle: { color: '#4499FF' },
          areaStyle: { color: 'rgba(68, 153, 255, 0.3)' },
          data: [{ value: downloadData, name: '下载量' }],
        },
      ],
      tooltip: {
        show: true,
        trigger: 'item',
        formatter: function (params) {
          if (params.seriesType === 'radar' && params.value) {
            let res = params.name + '<br/>';
            params.value.forEach((v, i) => {
              res += indicators[i].name + ': ' + v + '<br/>';
            });
            return res;
          } else if (params.seriesType === 'bar') {
            return params.name + '<br/>' + params.value;
          }
          return '';
        },
      },
    };
  };

  //打开应用详情
  function goDetail(item) {
    router.push({
      name: 'applicationDetails',
      query: { id: item.gameId },
    });
  }
  
  onMounted(() => {
    // 获取游戏类别
    fetchGameGenres();
    fetchAllDevice();
    fetchAllRankings();
    fetchData();
    fetchGenreRadarData();
    // 初始化市场现状图表
    if (marketNowChartRef.value) {
      const marketNowChart = echarts.init(marketNowChartRef.value);
      marketNowChart.setOption(getMarketNowOption());
      marketNowChart.resize();
    }
    // 初始化未来预测图表
    if (futurePredictionChartRef.value) {
      const futurePredictionChart = echarts.init(futurePredictionChartRef.value);
      futurePredictionChart.setOption(getFuturePredictionOption());
      futurePredictionChart.resize();
    }
    // 初始化合成图表（水平柱状图 + 新雷达图）
    if (combinedChartRef.value) {
      const combinedChart = echarts.init(combinedChartRef.value);
      combinedChart.setOption(getCombinedOption());
      combinedChart.resize();
    }
  });

  const currentRating = ref(0);
  // 设置默认值为近一年
  const value1 = ref<RangeValue | null>([dayjs().add(-1, 'year'), dayjs()]);

  // 获取市场情况两个雷达图数据
  const fetchData = async () => {
    try {
      loading.value = true;
      let startTime = '';
      let endTime = '';
      if (value1.value && value1.value.length === 2) {
        startTime = value1.value[0].format('YYYY-MM-DD');
        endTime = value1.value[1].format('YYYY-MM-DD');
      }
      
      // 处理付费情况参数
      let isPaidValue;
      if (selectedPayment.value.length === 0) {
        isPaidValue = 2; // 两个都有
      } else if (selectedPayment.value.length === 1) {
        if (selectedPayment.value[0] === '免费') {
          isPaidValue = 0;
        } else {
          isPaidValue = 1;
        }
      } else {
        isPaidValue = 2; // 两个都有
      }

      // 构建新接口所需的参数
      const params = {
        countryNames: selectedCountry.value || [],
        platformNames: selectedDevice.value || [],
        gameCategories: selectedGameCategory.value || [],
        isPaid: isPaidValue,
        startTime: startTime,
        endTime: endTime
      }

      console.log('发送的市场现状查询参数：',params)

      // 调用新接口
      const response = await currentMarketApi(params);

      console.log('市场现状数据：', response)
      
      // 更新随机数据
      ageData.value = generateRandomData();
      
      // 绑定数据
      console.log('API返回数据:', response);
      marketRadarData.value.downloads = Number(response.downloads) || 0;
      marketRadarData.value.income = Number(response.income) || 0;
      marketRadarData.value.app_numbers = Number(response.app_numbers) || 0;

      console.log('转换后数据：',marketRadarData)

      await nextTick();
      // 更新市场现状表
      if (marketNowChartRef.value) {
        const marketNowChart = echarts.init(marketNowChartRef.value);
        marketNowChart.setOption(getMarketNowOption());
        marketNowChart.resize();
      }

      // 更新未来预测图表
      if (futurePredictionChartRef.value) {
        const futurePredictionChart = echarts.init(futurePredictionChartRef.value);
        futurePredictionChart.setOption(getFuturePredictionOption());
        futurePredictionChart.resize();
      }    
    } catch (error) {
      console.error('数据获取失败:', error);
    } finally {
      loading.value = false;
    }
  };

  const maxTagPlaceholder = (omittedValues: any[]) => {
    return h('span', { class: 'ellipsis-tag' }, '...');
  };
  // const fetchRankingLists = async () => {
  //   try {
  //     loading.value = true; // 开始加载
  //     const response = await defHttp.get({
  //       url: '/shop/shopBehavior',
  //       params: {
  //         countryName: selectedCountry.value || '马来西亚',
  //         gameCategory: selectedGameCategory.value || '竞速',
  //         deviceName: selectedDevice.value,
  //         isPaid: selectedPayment.value,
  //         // 你可以根据实际需求补充其它参数
  //       },
  //     });
  //     freeDownloadList.value = response.appstoreRankingsFree || [];
  //     payDownloadList.value = response.appstoreRankingsGrossing || [];
  //     incomeList.value = response.appstoreRankingsPaid || [];
  //     console.log('获取榜单数据成功:', response);
  //   } catch (error) {
  //     console.error('获取榜单数据失败:', error);
  //     freeDownloadList.value = [];
  //     payDownloadList.value = [];
  //     incomeList.value = [];
  //   } finally {
  //     loading.value = false; // 结束加载
  //   }
  // };

  // const selectAllCountries = () => {
  //   if (selectedCountry.value.length === countries.length) {
  //     // 如果已全选，则清空
  //     selectedCountry.value = [];
  //   } else {
  //     // 否则全选
  //     selectedCountry.value = countries.map((item) => item.value);
  //   }
  // };

  function handleCountryChange(values: string[]) {
    // 如果点了 “all”：
    if (values.includes('all')) {
      // values 里只有 'all'（第一次点全选），就全选
      if (values.length === 1) {
        selectedCountry.value = countries.map(c => c.value)
      }
      // 否则再点一次 all（values 里有 all + 其他几项），就清空
      else {
        selectedCountry.value = []
      }
    } else {
      // 正常选其它项
      selectedCountry.value = values
    }
  }

  function handleDeviceChange(values: string[]) {
    if (values.includes('all')) {
      if (values.length === 1) {
        selectedDevice.value = devices.value.map(d => d.value)
      } else {
        selectedDevice.value = []
      }
    } else {
      selectedDevice.value = values
    }
  }

  function handleGameCategoryChange(values: string[]) {
    if (values.includes('all')) {
      if (values.length === 1) {
        selectedGameCategory.value = games.value.map(d => d.value)
      } else {
        selectedGameCategory.value = []
      }
    } else {
      selectedGameCategory.value = values
    }
  }

  function handlePaymentChange(values: string[]) {
    if (values.includes('all')) {
      if (values.length === 1) {
        selectedPayment.value = payments.map(d => d.value)
      } else {
        selectedPayment.value = []
      }
    } else {
      selectedPayment.value = values
    }
  }

</script>

<style lang="scss" scoped>
  .whole {
    padding: 10px;
  }
  .header::before {
    content: '|';
    color: #0893cf; /* 竖线颜色 */
    font-weight: 1000;
  }
  .header {
    font-size: 25px;
  }
  .input {
    background-color: white;
    padding: 20px;
    margin: 10px auto;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
  }
  .input-grid {
    display: grid;
    padding-left: 60px;
    padding-right: 60px;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
  }
  .input-box {
    width: 250px;
    font-size: 16px;
  }
  .bt-search {
    padding-left: 170px;
  }
  .bt-search a-button {
    font-size: 18px; /* 设置查询按钮中文字大小 */
    padding: 8px 16px; /* 调整按钮内边距，使其看起来更合适 */
  }
  .header1 {
    font-size: 15px;
  }
  .header1::before {
    content: '|';
    color: #0893cf; /* 竖线颜色 */
    font-weight: 1000;
  }
  .flex-container {
    display: flex;
  }
  .chart-container {
    display: flex;
    justify-content: space-around;
    width: 80%;
  }
  .market-now,
  .future-prediction {
    width: 45%;
    height: 400px;
  }
  .index-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 20%;
    font-size: 24px;
    color: #007bff;
  }
  .index-value {
    margin-bottom: 5px;
    font-size: 50px;
    text-align: center;
  }
  .index-label {
    color: black;
    font-size: 15px;
    text-align: center;
  }
  .user-data-container {
    display: flex;
    justify-content: space-around;
    padding: 20px 0;
  }
  /* 隐藏原有的 bar-chart 和 radar-chart */
  .bar-chart,
  .radar-chart {
    display: none;
  }
  .combined-chart {
    width: 100%;
    height: 500px;
    margin-top: 20px;
  }
  .download {
    display: flex;
    justify-content: space-between;
  }
  .input1 {
    width: 100%;
  }
  .input1-box {
    background-color: white;
    width: 80%;
    padding: 12px;
    margin: 12px 12px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    flex: 1;
    transition: all 0.3s ease;
    cursor: default; /* 保持默认指针 */
  }
  .input1-box:hover {
    background-color: #f0f7ff;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }
  .free,
  .pay,
  .income {
    flex: 1;
    // margin: 10px 10px;
  }
  .headerl {
    text-align: center;
    font-size: 20px;
    font-weight: bold;
  }
  .pic {
    width: 60px;
    height: 60px;
  }
  .info {
    margin-left: 20px;
  }
  .info-title {
    font-size: 17px;
    color: black;
    font-weight: 500;
  }
  .info-company {
    color: #c7c7c7;
    font-size: 15px;
  }
  .rating {
    display: flex;
    margin-top: 10px;
    align-items: center;
    width: 100%; /* 确保父容器宽度足够 */
  }
  .rating-left {
    display: flex;
    align-items: center;
  }
  .rating-value {
    margin-left: 10px;
    color: black;
  }
  .top-row {
    display: flex;
    align-items: center; /* 图片与标题垂直居中对齐 */
  }

  .rating-row {
    display: flex;
    justify-content: space-between; /* 左右两边分布 */
    align-items: center; /* 垂直居中 */
  }
  .right-info {
    text-align: right; /* 右侧文本右对齐 */
  }
  /* 防止数字或价格换行 */
  .download-count,
  .price {
    white-space: nowrap;
  }
  .price {
    padding-bottom: 10px;
    color: #a0a0a0;
  }
  .rating-count {
    color: #c3c7ce;
  }
  .download-count {
    margin-left: auto;
    color: #c3c7ce;
  }
  .sort {
    margin-top: 200px;
    margin-left: 50px;
  }
  .num {
    margin-bottom: 200px;
    font-size: 30px;
  }
  .ranking-container {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }
  .ranking {
    margin-right: 6px;
    font-size: 17px;
    font-weight: bold;
    width: 28px;
    text-align: center;
  }
</style>
