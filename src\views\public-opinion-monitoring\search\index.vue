<template>
  <div>
    <!-- <div class="chart-container"> -->
    <!-- 顶部栏 -->
    <!-- <div class="top-bar" >
                <div class="game-info">
                    <img style=" margin-left: 10px;width: 60px; height: auto; " src="./image/mihayo.png" alt="崩坏3" class="logo" />

                    <div class="game-details">
                        <h2 class="game-title">崩坏3</h2>
                        <p class="game-subtitle">miHoYo 米哈游</p>
                    </div>
                </div> -->
    <!-- 竖线 -->
    <!-- <div class="vertical-line"></div> -->

    <!-- 搜索栏 -->
    <!-- <div class="search-bar">
                    <div class="search-container">
                        <Icon icon="ant-design:search-outlined" class="search-icon" />
                        <input type="text" placeholder="搜索游戏" class="search-input" />
                    </div>
                </div>
            </div>
        </div> -->

    <div class="page-container" ref="pageContainer">
      <GameTitle />
      <!--      <div class="public-wrapper">-->
      <!--        &lt;!&ndash; 顶部栏 &ndash;&gt;-->
      <!--        <div class="top-bar">-->
      <!--          <div class="game-info">-->
      <!--            <img src="/src/assets/images/game2.png" alt="崩坏3" class="logo" />-->
      <!--            <div class="game-details">-->
      <!--              <h2 class="game-title">崩坏3</h2>-->
      <!--              <p class="game-subtitle">miHoYo 米哈游</p>-->
      <!--            </div>-->
      <!--          </div>-->
      <!--          &lt;!&ndash; 竖线 &ndash;&gt;-->
      <!--          <div class="vertical-line"></div>-->

      <!--          &lt;!&ndash; 搜索栏 &ndash;&gt;-->
      <!--          <div class="search-bar">-->
      <!--            <div class="search-container">-->
      <!--              <Icon icon="ant-design:search-outlined" class="search-icon" />-->
      <!--              <input type="text" placeholder="搜索游戏" class="search-input" />-->
      <!--            </div>-->
      <!--          </div>-->

      <!--          &lt;!&ndash; 方案栏 &ndash;&gt;-->
      <!--          <div class="prog-bar">-->
      <!--            <select class="select-plan">-->
      <!--              <option value="" disabled selected>选择监控方案</option>-->
      <!--              <option value="plan1">方案一</option>-->
      <!--              <option value="plan2">方案二</option>-->
      <!--              <option value="unknown">未知</option>-->
      <!--            </select>-->
      <!--            <a-button type="link" class="configure-button" @click="configurePlan">配置监控方案</a-button>-->
      <!--          </div>-->
      <!--        </div>-->
      <!--      </div>-->
      <!-- 主内容区域：舆情内容表格 -->
      <div>
        <div class="main-content">
          <div class="opinion-table-container">
            <!-- 添加筛选区域 -->
            <div class="filter-section">
              <div class="filter-row">
                <div class="filter-item time-filter">
                  <label>检测时间：</label>
                  <a-space direction="vertical" :size="12">
                    <a-range-picker
                      v-model:value="detectTime"
                      :presets="rangePresets"
                      @change="onRangeChange"
                    />
                  </a-space>
                </div>
                <div class="filter-item source-filter">
                  <label>信息来源渠道：</label>
                  <a-select
                    v-model:value="sourceChannel"
                    mode="multiple"
                    style="width: 100%"
                    placeholder="选择信息来源渠道"
                    allowClear
                    :getPopupContainer="() => pageContainer"
                  >
                    <a-select-option v-for="channel in channelList" :key="channel" :value="channel">{{ channel }}</a-select-option>
                  </a-select>
                </div>
              </div>
              <div class="filter-row">
                <div class="filter-item keyword-filter">
                  <label>言论内容关键词：</label>
                  <div class="search-wrapper">
                    <a-input
                      v-model:value="keywordFilter"
                      style="width: 100%"
                      placeholder="请输入关键词，使用空格分隔，最多输入100个字符"
                      allowClear
                    />
                  </div>
                </div>
              </div>
              <div class="filter-row">
                <div class="filter-item exclude-filter">
                  <label>言论内容排除词：</label>
                  <div class="search-wrapper">
                    <a-input
                      v-model:value="excludeFilter"
                      style="width: 100%"
                      placeholder="请输入排除词，使用空格分隔，最多输入100个字符"
                      allowClear
                    />
                    <a-button type="primary" class="search-button" @click="filterTableData">查询</a-button>
                  </div>
                </div>
              </div>
            </div>
            <div class="table-header">
              <h2>舆情内容</h2>
            </div>
            <!-- Ant Design Vue Table组件 -->
            <a-table
              :columns="columns"
              :data-source="filteredTableData"
              :pagination="{
                current: currentPage,
                pageSize: itemsPerPage,
                total: totalPage,
                pageSizeOptions: pageSizeOptions.map(String),
                showSizeChanger: true,
                showTotal: (total) => `共 ${total} 条数据`,
              }"
              :loading="loading"
              @change="handleTableChange"
              bordered
              size="middle"
              :row-key="(record) => record.id"
              :class="'custom-table'"
            >
              <!-- 标题列自定义渲染 -->
              <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'title'">
                  <a :href="record.pageUrl" class="title-link" target="_blank" :title="record.title">
                    <span class="link-text">{{ record.title }}</span>
                    <i class="external-icon">↗</i>
                  </a>
                </template>
                <!-- 情感类别列自定义渲染 -->
                <template v-if="column.dataIndex === 'sentiment'">
                  <div class="sentiment-container">
                    <span :class="`${record.sentiment || '未知'}-sentiment sentiment-tag`">
                      {{ record.sentiment || '未知' }}
                    </span>
                  </div>
                </template>
              </template>
              <!-- 空数据显示 -->
              <template #emptyText>
                <div class="empty-data-container">
                  <i class="empty-icon">🔍</i>
                  <p>没有找到匹配的舆情数据</p>
                  <p class="empty-data-tip">请尝试调整筛选条件后再次查询</p>
                </div>
              </template>
            </a-table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" name="basic-table-demo1" setup>
  import { ref, watch, onMounted } from 'vue';
  import 'echarts-wordcloud';
  import dayjs, { Dayjs } from 'dayjs';
  import { getSentimentDetailListApi, getCustomNameByPlanIdApi, saveWebpageApi, getWebpageApi } from '@/api/public-opinion-monitoring/today';
  import { useUserStore } from '/@/store/modules/user';
  // 导入gameTitle组件，使用script setup语法的组件不需要导出
  import { defineAsyncComponent } from 'vue';
  const GameTitle = defineAsyncComponent(() => import('../components/gameTitle.vue'));

  // 获取用户store
  const userStore = useUserStore();

  // 创建页面容器引用，用于下拉菜单定位
  const pageContainer = ref(null);

  // 从localStorage获取planId
  const planId = ref(localStorage.getItem('currentPlanId') || '');

  // 添加事件监听器，监听localStorage的变化
  window.addEventListener('storage', (event) => {
    if (event.key === 'currentPlanId' && event.newValue !== planId.value) {
      // console.log('localStorage currentPlanId变化:', event.newValue, '当前planId:', planId.value);
      planId.value = event.newValue || '';
    }
  });

  // 添加自定义事件监听，用于在同一窗口内监听planId变化
  window.addEventListener('planIdChanged', (event: CustomEvent) => {
    const newPlanId = event.detail;
    // console.log('planIdChanged事件触发，新planId:', newPlanId, '当前planId:', planId.value);
    if (newPlanId !== planId.value) {
      planId.value = newPlanId || '';
      // console.log('planId已更新为:', planId.value);
    }
  });

  // 添加gameId变化监听，当游戏切换时清空筛选条件
  window.addEventListener('gameIdChanged', (event: CustomEvent) => {
    const newGameId = event.detail;
    // console.log('游戏切换事件触发，新游戏ID:', newGameId);
    // 当游戏改变时，清空当前的筛选条件，等待新的planId触发恢复
    if (newGameId) {
      // 重置初始化状态
      isInitialized.value = false;
      // console.log('游戏切换：重置初始化状态为false');

      // 清空筛选条件和渠道列表
      detectTime.value = null;
      sourceChannel.value = [];
      keywordFilter.value = '';
      excludeFilter.value = '';
      channelList.value = []; // 清空渠道列表

      // 清空planId，这样当新的planId设置时会触发watch
      planId.value = '';
      // console.log('游戏切换：清空planId，等待新planId设置');
    }
  });

  // 时间选择框的便捷选项设置
  type RangeValue = [Dayjs, Dayjs];
  const rangePresets = ref([
    { label: '当天', value: [dayjs().startOf('day'), dayjs().endOf('day')] },
    { label: '最近三天', value: [dayjs().add(-3, 'd'), dayjs()] },
    { label: '最近一周', value: [dayjs().add(-7, 'd'), dayjs()] },
    { label: '最近一个月', value: [dayjs().add(-1, 'month'), dayjs()] },
    { label: '最近三个月', value: [dayjs().add(-3, 'month'), dayjs()] },
    { label: '最近六个月', value: [dayjs().add(-6, 'month'), dayjs()] },
    { label: '最近一年', value: [dayjs().add(-1, 'year'), dayjs()] },
    { label: '最近两年', value: [dayjs().add(-2, 'year'), dayjs()] },
    { label: '最近三年', value: [dayjs().add(-3, 'year'), dayjs()] },
  ]);
  const onRangeChange = (dates: RangeValue) => {
    // console.log('日期选择器变化:', dates);
    detectTime.value = dates;
  };

  // 判断是否选择的是当天
  const isToday = (dates: any) => {
    if (!dates || dates.length !== 2) return false;
    const start = dayjs(dates[0]);
    const end = dayjs(dates[1]);
    const today = dayjs();

    // 检查是否是同一天，并且是今天
    return start.isSame(today, 'day') && end.isSame(today, 'day') && start.isSame(end, 'day');
  };

  // 添加渠道列表
  const channelList = ref<string[]>([]);

  // 定义接口返回数据的类型
  interface SentimentItem {
    id: number | string;
    title: string;
    content: string;
    source: string;
    sentiment: string;
    sentiment_type: string;
    url: string;
    time: string;
    channel_custom_name: string;
    postTime: string;
    sentimentType: 'positive' | 'negative' | 'neutral';
    pageUrl: string;
  }

  // 表格数据
  const tableData = ref<SentimentItem[]>([]);
  const filteredTableData = ref<SentimentItem[]>([]);
  const loading = ref(false);

  // 分页相关
  const itemsPerPage = ref(10); // 默认每页显示10条
  const currentPage = ref(1);
  const totalPage = ref(0);
  const pageSizeOptions = [10, 20, 50, 100, 200, 500, 1000]; // 增加分页选项

  // 定义表格列
  const columns = [
    {
      title: '标题',
      dataIndex: 'title',
      key: 'title',
      width: '20%',
      ellipsis: true,
    },
    {
      title: '言论内容',
      dataIndex: 'content',
      key: 'content',
      width: '35%',
      ellipsis: true,
    },
    {
      title: '信息来源渠道',
      dataIndex: 'channel_custom_name',
      key: 'channel_custom_name',
      width: '15%',
    },
    {
      title: '检测时间',
      dataIndex: 'postTime',
      key: 'postTime',
      width: '15%',
      sorter: (a: SentimentItem, b: SentimentItem) => {
        try {
          const dateA = a.postTime ? new Date(a.postTime).getTime() : 0;
          const dateB = b.postTime ? new Date(b.postTime).getTime() : 0;
          return dateA - dateB;
        } catch (error) {
          console.error('日期排序错误:', error);
          return 0;
        }
      },
      customRender: ({ text }: { text: string }) => {
        try {
          return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '';
        } catch (error) {
          console.error('日期格式化错误:', error, text);
          return text || '';
        }
      },
    },
    {
      title: '情感类别',
      dataIndex: 'sentiment',
      key: 'sentiment',
      width: '15%',
      filters: [
        { text: '正面', value: '正面' },
        { text: '负面', value: '负面' },
        { text: '中性', value: '中性' },
        { text: '未知', value: '未知' },
      ],
      onFilter: (value: string, record: SentimentItem) => {
        try {
          return record.sentiment === value || (!record.sentiment && value === '未知');
        } catch (error) {
          console.error('情感类别过滤错误:', error);
          return false;
        }
      },
    },
  ];

  // 处理表格变化（分页、排序、筛选）
  const handleTableChange = (pagination: any) => {
    currentPage.value = pagination.current || 1;
    itemsPerPage.value = pagination.pageSize || 10;
    // 调用API获取新页的数据
    fetchSentimentDetailList();
  };

  // 获取情感详情列表
  const fetchSentimentDetailList = async () => {
    try {
      // 先清空表格数据
      tableData.value = [];
      filteredTableData.value = [];
      loading.value = true;

      if (!planId.value || planId.value === '') {
        return;
      }

      let planIdList = [parseInt(planId.value)];

      // 如果没有选择渠道或选择了所有渠道，则一次性获取所有数据
      if (sourceChannel.value.length === 0 ||
          (channelList.value.length > 0 && sourceChannel.value.length === channelList.value.length)) {
        //参数
        let timeParams = {};
        if (detectTime.value && detectTime.value.length === 2) {
          if (isToday(detectTime.value)) {
            // 如果选择的是当天，只传一个当前时间参数
            timeParams = {
              postTime: dayjs().valueOf(), // 传递当前时间戳
            };
          } else {
            // 其他情况传递时间范围
            timeParams = {
              postTimeStart: dayjs(detectTime.value[0]).valueOf(),
              postTimeEnd: dayjs(detectTime.value[1]).valueOf(),
            };
          }
        }

        const params = {
          planIds: planIdList,
          channelCustomName: undefined, // 不传渠道名称，表示获取所有渠道
          ...timeParams,
          // 处理关键词，使用空格分隔，作为并集关系（OR）传递给后端
          includeKeys: keywordFilter.value
            ? keywordFilter.value
                .split(/\s+/) // 只使用空格分割
                .map(item => item.trim()) // 去除每个关键词的前后空格
                .filter(Boolean) // 过滤空字符串
                .join(',') // 重新用逗号连接，作为OR关系传递
            : '',
          // 处理排除词，使用空格分隔，作为并集关系（OR）传递给后端
          excludeKeys: excludeFilter.value
            ? excludeFilter.value
                .split(/\s+/) // 只使用空格分割
                .map(item => item.trim()) // 去除每个排除词的前后空格
                .filter(Boolean) // 过滤空字符串
                .join(',') // 重新用逗号连接，作为OR关系传递
            : '',
          pageSize: itemsPerPage.value,
          pageNum: currentPage.value,
        };

        //获取后端数据
        const response = await getSentimentDetailListApi(params);

        if (response) {
          // 处理数据字段映射，确保字段名称匹配
          const processedData = (response.detailList || []).map((item: any, index: number) => ({
            ...item,
            // 确保id字段存在，用于表格row-key
            id: item.id || item.uuid || item.key || `row-${index}`,
            // 确保pageUrl字段存在，如果不存在则使用url字段
            pageUrl: item.pageUrl || item.url || '#',
            // 确保其他必要字段存在
            channel_custom_name: item.channel_custom_name || item.channelCustomName || item.source || '',
            postTime: item.postTime || item.time || item.createTime || '',
            sentiment: item.sentiment || item.sentimentType || '未知'
          }));

          filteredTableData.value = processedData;
          totalPage.value = response.totalCount || 0;
          currentPage.value = response.currentPage || 1;
        }
      } else {
        // 如果选择了特定渠道，则为每个渠道发送单独的请求
        let allDetailList: SentimentItem[] = [];
        let totalCount = 0;

        // 为每个选中的渠道发送请求
        const requests = sourceChannel.value.map(channel => {
          let timeParams = {};
          if (detectTime.value && detectTime.value.length === 2) {
            if (isToday(detectTime.value)) {
              // 如果选择的是当天，只传一个当前时间参数
              timeParams = {
                postTime: dayjs().valueOf(), // 传递当前时间戳
              };
            } else {
              // 其他情况传递时间范围
              timeParams = {
                postTimeStart: dayjs(detectTime.value[0]).valueOf(),
                postTimeEnd: dayjs(detectTime.value[1]).valueOf(),
              };
            }
          }

          const params = {
            planIds: planIdList,
            channelCustomName: channel,
            ...timeParams,
            // 处理关键词，使用空格分隔，作为并集关系（OR）传递给后端
            includeKeys: keywordFilter.value
              ? keywordFilter.value
                  .split(/\s+/) // 只使用空格分割
                  .map(item => item.trim()) // 去除每个关键词的前后空格
                  .filter(Boolean) // 过滤空字符串
                  .join(',') // 重新用逗号连接，作为OR关系传递
              : '',
            // 处理排除词，使用空格分隔，作为并集关系（OR）传递给后端
            excludeKeys: excludeFilter.value
              ? excludeFilter.value
                  .split(/\s+/) // 只使用空格分割
                  .map(item => item.trim()) // 去除每个排除词的前后空格
                  .filter(Boolean) // 过滤空字符串
                  .join(',') // 重新用逗号连接，作为OR关系传递
              : '',
            pageSize: itemsPerPage.value,
            pageNum: currentPage.value,
          };

          return getSentimentDetailListApi(params);
        });

        // 等待所有请求完成
        const responses = await Promise.all(requests);

        // 合并结果
        let globalIndex = 0;
        responses.forEach(response => {
          if (response && response.detailList) {
            // 处理数据字段映射
            const processedData = response.detailList.map((item: any, index: number) => ({
              ...item,
              // 确保id字段存在，用于表格row-key
              id: item.id || item.uuid || item.key || `row-${globalIndex + index}`,
              // 确保pageUrl字段存在，如果不存在则使用url字段
              pageUrl: item.pageUrl || item.url || '#',
              // 确保其他必要字段存在
              channel_custom_name: item.channel_custom_name || item.channelCustomName || item.source || '',
              postTime: item.postTime || item.time || item.createTime || '',
              sentiment: item.sentiment || item.sentimentType || '未知'
            }));

            globalIndex += response.detailList.length;
            allDetailList = [...allDetailList, ...processedData];
            totalCount += response.totalCount || 0;
          }
        });

        // 更新表格数据
        filteredTableData.value = allDetailList;
        totalPage.value = totalCount;
      }

      // 如果没有获取到数据
      if (!filteredTableData.value || filteredTableData.value.length === 0) {
        tableData.value = [];
        filteredTableData.value = [];
      }
    } catch (error) {
      // console.error('获取情感详情列表出错:', error);
      tableData.value = [];
      filteredTableData.value = [];
    } finally {
      loading.value = false;
    }
  };

  // 获取渠道列表
  const fetchChannelList = async () => {
    try {
      if (!planId.value || planId.value === '') {
        return;
      }

      const response = await getCustomNameByPlanIdApi({
        planId: parseInt(planId.value),
      });

      // 根据实际返回的数据结构进行处理
      if (response && response.results && Array.isArray(response.results)) {
        // 直接使用 response.results
        channelList.value = response.results;
        // 不自动选择渠道，让恢复逻辑来处理
        // console.log('获取到的渠道列表:', channelList.value);
      } else if (response && response.result && response.result.results && Array.isArray(response.result.results)) {
        // 兼容嵌套在 result.results 中的情况
        channelList.value = response.result.results;
        // 不自动选择渠道，让恢复逻辑来处理
        // console.log('获取到的渠道列表(嵌套):', channelList.value);
      } else {
        // console.error('获取渠道列表返回数据格式不支持:', response);
        channelList.value = [];
        sourceChannel.value = [];
      }
    } catch (error) {
      // console.error('获取渠道列表出错:', error);
      channelList.value = [];
      sourceChannel.value = [];
    }
  };

  // 保存筛选条件到后端
  const saveFilterConditions = async () => {
    try {
      const userInfo = userStore.getUserInfo;
      if (!userInfo || !userInfo.username || !planId.value) {
        return;
      }

      const filterData = {
        planId: planId.value,
        detectTime: detectTime.value,
        sourceChannel: sourceChannel.value,
        keywordFilter: keywordFilter.value,
        excludeFilter: excludeFilter.value,
      };


      await saveWebpageApi({
        redisKey: userInfo.username,
        field: `search_filters_${planId.value}`, // 为每个游戏单独保存筛选条件
        value: [filterData], // 按照接口要求，value是数组格式
      });
    } catch (error) {
      // console.error('保存筛选条件出错:', error);
    }
  };

  // 从后端恢复筛选条件
  const restoreFilterConditions = async () => {
    const userInfo = userStore.getUserInfo;
    if (!userInfo || !userInfo.username || !planId.value) {
      // 即使无法恢复，也要标记初始化完成
      setTimeout(() => {
        isInitialized.value = true;
      }, 200);
      return;
    }

    isRestoring.value = true; // 标记正在恢复

    try {
      const response = await getWebpageApi({
        redisKey: userInfo.username,
        field: `search_filters_${planId.value}`, // 为每个游戏单独获取筛选条件
      }, {
        errorMessageMode: 'none' // 静默处理错误，不显示错误提示
      });


      // 修正数据解析逻辑：API返回的是对象格式 {field: [data]}
      const fieldKey = `search_filters_${planId.value}`;
      if (response && response[fieldKey] && Array.isArray(response[fieldKey]) && response[fieldKey].length > 0) {
        const filterData = response[fieldKey][0];

        // 恢复检测时间
        if (filterData.detectTime && Array.isArray(filterData.detectTime) && filterData.detectTime.length === 2) {
          // 将字符串格式的日期转换为Dayjs对象
          const convertedDates = [
            dayjs(filterData.detectTime[0]),
            dayjs(filterData.detectTime[1])
          ];
          detectTime.value = convertedDates;
        }

        // 恢复关键词
        if (filterData.keywordFilter !== undefined) {
          keywordFilter.value = filterData.keywordFilter;
        }

        // 恢复排除词
        if (filterData.excludeFilter !== undefined) {
          excludeFilter.value = filterData.excludeFilter;
        }

        // 恢复信息来源渠道（需要等渠道列表加载完成后再设置）
        if (filterData.sourceChannel && Array.isArray(filterData.sourceChannel)) {
          // 延迟设置，确保渠道列表已加载
          setTimeout(() => {
            if (channelList.value.length > 0) {
              const validChannels = filterData.sourceChannel.filter((channel: string) =>
                channelList.value.includes(channel)
              );
              sourceChannel.value = validChannels;
            } else {
              // 如果当前游戏没有渠道，清空渠道选择
              sourceChannel.value = [];
            }
          }, 100);
        } else {
          // 如果没有保存的渠道选择，延迟设置默认选择所有渠道（如果有的话）
          setTimeout(() => {
            if (channelList.value.length > 0) {
              sourceChannel.value = [...channelList.value];
            } else {
              // 如果当前游戏没有渠道，保持空数组
              sourceChannel.value = [];
            }
          }, 100);
        }
      } else {
        // 没有找到保存的筛选条件，使用默认值
        handleNoSavedFilters();
      }
    } catch (error: any) {
      // 所有错误都当作"没有保存的筛选条件"处理，使用默认值
      // console.log(`方案 ${planId.value} 没有保存的筛选条件，使用默认值`);
      handleNoSavedFilters();
    } finally {
      // 延迟重置标记，确保所有恢复操作完成
      setTimeout(() => {
        isRestoring.value = false;
        isInitialized.value = true; // 标记初始化完成
      }, 200);
    }
  };

  // 处理没有保存筛选条件的情况
  const handleNoSavedFilters = () => {
    // 设置默认的筛选条件
    resetToDefaultFilters();
    // 默认选择所有渠道（如果有的话）
    setTimeout(() => {
      if (channelList.value.length > 0) {
        sourceChannel.value = [...channelList.value];
      } else {
        sourceChannel.value = [];
      }
    }, 100);
  };

  // 重置为默认筛选条件
  const resetToDefaultFilters = () => {
    // 设置默认时间范围（最近7天）
    const endDate = dayjs();
    const startDate = endDate.subtract(7, 'day');
    detectTime.value = [startDate, endDate];

    // 清空其他筛选条件
    sourceChannel.value = [];
    keywordFilter.value = '';
    excludeFilter.value = '';
  };

  // 页面加载时的初始化工作
  onMounted(() => {
    // console.log('页面加载完成，planId:', planId.value);
    // 不需要在这里调用 restoreFilterConditions
    // 因为我们已经在 watch planId 中添加了恢复逻辑
    // 不需要在这里调用 fetchChannelList 和 fetchSentimentDetailList
    // 因为我们已经在 watch planId 中添加了 immediate: true
  });

  // 筛选条件
  const detectTime = ref<any>(null); // 修改为null，避免ARangePicker类型警告
  const sourceChannel = ref<string[]>([]);
  const keywordFilter = ref('');
  const excludeFilter = ref('');

  // 标记是否正在恢复筛选条件，避免在恢复过程中触发保存
  const isRestoring = ref(false);

  // 标记是否已经完成初始化（包括恢复筛选条件），避免在初始化过程中保存默认值
  const isInitialized = ref(false);

  // 根据筛选条件过滤数据
  const filterTableData = () => {
    // 重置分页参数
    currentPage.value = 1;
    // 调用API获取数据
    fetchSentimentDetailList();
    // 只有在非恢复状态且已完成初始化后才保存筛选条件
    if (!isRestoring.value && isInitialized.value) {
      saveFilterConditions();
    }
  };

  // 监听筛选条件变化
  watch(
    [detectTime, sourceChannel, keywordFilter, excludeFilter],
    () => {
      filterTableData();
    },
    { deep: true }
  );

  // 监听 planId 变化，重新获取渠道列表和舆情数据
  watch(
    planId,
    async (newVal) => {
      // console.log('planId变化:', newVal);
      if (newVal) {
        // 重置初始化状态
        isInitialized.value = false;
        // console.log('planId变化：重置初始化状态为false');

        // 清空筛选条件
        detectTime.value = null;
        sourceChannel.value = [];
        keywordFilter.value = '';
        excludeFilter.value = '';

        try {
          // 重新获取渠道列表
          // console.log('开始获取渠道列表...');
          await fetchChannelList();
          // console.log('渠道列表获取完成');
        } catch (error) {
          // 即使获取渠道列表失败，也要继续执行后续逻辑
          // console.warn('获取渠道列表失败，但继续执行恢复逻辑:', error);
        }

        // 恢复筛选条件（即使没有渠道也要恢复其他筛选条件）
        // console.log('开始恢复筛选条件...');
        await restoreFilterConditions();
        // console.log('筛选条件恢复完成');

        // 重新获取舆情数据
        // console.log('开始获取舆情数据...');
        fetchSentimentDetailList();
      } else {
        // console.log('planId为空，跳过初始化');
        // 当planId为空时，也要标记为已初始化，这样用户手动修改筛选条件时可以保存
        setTimeout(() => {
          isInitialized.value = true;
          // console.log('planId为空时延迟设置初始化完成');
        }, 500);
      }
    },
    { immediate: true } // 立即执行一次
  );
</script>
<style scoped>
  /* 页面整体布局 */
  .page-container {
    font-family: Arial, sans-serif;
    width: 100%;
    padding: 20px;
    background-color: #f9f9f9;
  }

  /* 顶部外框 */
  .public-wrapper {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    padding: 20px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  /* 顶部栏 */
  .top-bar {
    display: flex;
    align-items: center;
    width: 100%;
  }

  .game-info {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .logo {
    width: 70px;
    height: 70px;
    border-radius: 10px;
  }

  .game-details {
    display: flex;
    flex-direction: column;
  }

  .game-title {
    font-size: 20px;
    font-weight: bold;
    margin: 0;
  }

  .game-subtitle {
    font-size: 14px;
    color: gray;
    margin: 0;
  }

  /* 竖线样式 */
  .vertical-line {
    width: 1px;
    height: 80px;
    background-color: #ccc;
    margin-left: 50px;
    margin-right: 50px;
  }

  /* 搜索栏 */
  .search-bar {
    flex: 1;
    width: 500px;
    margin: 0 20px;
  }

  .search-container {
    position: relative;
    display: flex;
    align-items: center;
  }

  .search-icon {
    position: absolute;
    left: 10px;
    color: #888;
    font-size: 16px;
  }

  .search-input {
    padding: 8px 8px 8px 40px;
    border: 1px solid #ccc;
    border-radius: 50px;
    width: 100%;
    font-size: 16px;
    background-color: #f1f3f5;
  }

  /* 方案栏 */
  .prog-bar {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-left: auto;
  }

  .select-plan {
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 5px;
    width: 200px;
  }

  /* 配置监控方案按钮样式 */
  .configure-button {
    color: #018ffb;
    /* 蓝色 */
    font-size: 14px;
    padding: 0;
    /* 去掉默认内边距，保持文字按钮风格 */
    background: transparent;
    /* 透明背景 */
    border: none;
    /* 去掉边框 */
    cursor: pointer;
    margin-left: 10px;
    /* 与选择框保持一定距离 */
  }

  .configure-button:hover {
    color: #0170c9;
    /* 悬停时颜色变深 */
    text-decoration: underline;
    /* 悬停时添加下划线 */
  }

  .chart-container {
    margin-top: 20px;
    border: 5px;
    margin: 10px 20px;
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    /* 设置圆角 */
    overflow: hidden;
    /* 确保内容不会溢出圆角 */
    border-radius: 8px;
    box-shadow:
      0px 0px 0px rgba(77, 85, 117, 0.05),
      0px 3px 7px rgba(77, 85, 117, 0.05),
      0px 5px 14px rgba(77, 85, 117, 0.04),
      0px 13px 18px rgba(77, 85, 117, 0.03),
      0px 20px 20px rgba(77, 85, 117, 0.01),
      0px 35px 30px rgba(77, 85, 117, 0);
  }

  .chart-container1 {
    display: flex;
    gap: 10px;
    align-items: center;
    margin-bottom: 10px;
  }

  .chart-containertitle {
    border: 3px solid #0893cf;
    border-top: none;
    border-right: none;
    border-bottom: none;
    padding-left: 10px;
    margin-bottom: 20px;
    font-size: medium;
  }

  /* 主内容区域 */
  .main-content {
    margin-bottom: 20px;
  }

  /* 舆情内容表格容器 */
  .opinion-table-container {
    padding: 20px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  /* 表格头部 */
  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }

  .table-header h2 {
    font-size: 18px;
    font-weight: bold;
    margin: 0;
    color: #333;
    border-bottom: 4px solid #018ffb;
    width: 72px;
  }

  .table-actions {
    display: flex;
    gap: 10px;
  }

  .action-button {
    padding: 8px 16px;
    background-color: #018ffb;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s;
  }

  .action-button:hover {
    background-color: #0170c9;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(1, 143, 251, 0.3);
  }

  /* 添加筛选区域样式 */
  .filter-section {
    margin-bottom: 20px;
    padding: 24px;
    /* 增加内边距 */
    background-color: #fff;
    border-radius: 8px;
    /* 增加圆角 */
    /* 调整阴影 */
  }

  .filter-row {
    display: flex;
    gap: 32px;
    /* 增加列间距 */
    margin-bottom: 16px;
    /* 增加行间距 */
    align-items: center;
  }

  .filter-row:last-child {
    margin-bottom: 0;
  }

  .filter-item {
    display: flex;
    align-items: center;
  }

  .time-filter {
    flex: 0.8;
    /* 调整检测时间宽度比例 */
  }

  .source-filter {
    flex: 1.2;
    /* 调整信息来源渠道宽度比例 */
  }

  .keyword-filter,
  .exclude-filter {
    flex: 1;
    width: 100%;
  }

  .filter-item label {
    min-width: 120px;
    /* 增加标签宽度 */
    color: #333;
    font-size: 15px;
    white-space: nowrap;
    font-weight: bold;
    margin-right: 12px;
    /* 增加标签和输入框的间距 */
  }

  .filter-input,
  .filter-select {
    flex: 1;
    padding: 10px 16px;
    /* 增加输入框内边距 */
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    color: #333;
    background-color: #fff;
    min-width: 180px;
    /* 调整最小宽度 */
    transition: all 0.3s;
    /* 添加过渡效果 */
  }

  .filter-input:hover,
  .filter-select:hover {
    border-color: #018ffb;
  }

  .filter-input:focus,
  .filter-select:focus {
    border-color: #018ffb;
    box-shadow: 0 0 0 2px rgba(1, 143, 251, 0.1);
    outline: none;
  }

  .search-wrapper {
    display: flex;
    gap: 12px;
    /* 增加搜索框和按钮的间距 */
    flex: 1;
  }

  .search-wrapper .filter-input {
    flex: 1;
  }

  .search-button {
    padding: 0 24px;
    height: 40px;
    font-size: 14px;
    font-weight: 500;
    border-radius: 6px;
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.02);
    transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    min-width: 120px;
  }

  .search-button:hover {
    background-color: #1677ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(22, 119, 255, 0.3);
  }

  .search-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.02);
  }

  /* 自定义表格样式 */
  .custom-table {
    width: 100%;
    font-size: 14px;
  }

  /* 表头样式 */
  :deep(.custom-table .ant-table-thead > tr > th) {
    background-color: #c2e8f8;
    color: #333;
    font-weight: bold;
    text-align: center;
    padding: 12px 16px;
  }

  /* 表格单元格样式 */
  :deep(.custom-table .ant-table-tbody > tr > td) {
    padding: 12px 16px;
    text-align: center;
    color: #666;
    vertical-align: middle;
    border-bottom: 1px solid #eee;
  }

  /* 斑马纹样式 */
  :deep(.custom-table .ant-table-tbody > tr:nth-child(odd)) {
    background-color: #ffffff;
  }

  :deep(.custom-table .ant-table-tbody > tr:nth-child(even)) {
    background-color: #dcf2fb;
  }

  /* 悬停样式 */
  :deep(.custom-table .ant-table-tbody > tr:hover > td) {
    background-color: #e6f7ff;
  }

  /* 表格列宽设置 */
  :deep(.custom-table .ant-table-column-title) {
    white-space: nowrap;
  }

  /* 内容列样式 */
  :deep(.custom-table .ant-table-cell-ellipsis) {
    white-space: normal;
    overflow: visible;
    max-height: 100px;
    overflow-y: auto;
    text-align: center;
  }

  /* 标题链接样式 */
  .title-link {
    color: #018ffb;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.3s;
  }

  .title-link:hover {
    background-color: rgba(1, 143, 251, 0.1);
    color: #0170c9;
  }

  .link-text {
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .external-icon {
    font-size: 12px;
    opacity: 0.7;
    transition: all 0.3s;
  }

  .title-link:hover .external-icon {
    opacity: 1;
    transform: translate(2px, -2px);
  }

  /* 来源列样式 */
  .source-column {
    text-align: center;
    /* 来源居中对齐 */
  }

  /* 时间列和情感类别列样式 */
  .time-column,
  .sentiment-column {
    text-align: center;
    /* 居中对齐 */
  }

  /* 表头样式 */
  .opinion-table th {
    background-color: #e6f7ff;
    font-weight: bold;
    color: #333;
    text-align: center;
    /* 表头居中对齐 */
    padding: 12px 16px;
    white-space: nowrap;
    /* 防止表头换行 */
  }

  /* 情感类别样式 */
  .sentiment-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }

  .sentiment-tag {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: bold;
    text-align: center;
    min-width: 60px;
  }

  .正面-sentiment {
    color: #52c41a;
    background-color: rgba(82, 196, 26, 0.1);
  }

  .负面-sentiment {
    color: #e02323;
    background-color: rgba(224, 35, 35, 0.1);
  }

  .中性-sentiment {
    color: #8c8c8c;
    background-color: rgba(140, 140, 140, 0.1);
  }

  .未知-sentiment {
    color: #8c8c8c;
    background-color: rgba(140, 140, 140, 0.1);
  }

  .edit-button {
    background: none;
    border: none;
    padding: 4px;
    cursor: pointer;
    color: #018ffb;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: background-color 0.2s;
  }

  /* 确保下拉菜单正确显示 */
  :deep(.ant-select-dropdown),
  :deep(.ant-picker-dropdown) {
    position: absolute !important;
    z-index: 1050 !important;
  }

  .edit-button:hover {
    background-color: rgba(1, 143, 251, 0.1);
  }

  .edit-icon {
    font-size: 14px;
    font-style: normal;
    transform: scaleX(-1);
  }

  /* 分页样式 */
  .pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    padding: 15px 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  .pagination-size {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .pagination-label {
    color: #666;
    font-size: 14px;
  }

  .page-size-select {
    padding: 6px 12px;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    background-color: #fff;
    color: #333;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s;
  }

  .page-size-select:hover {
    border-color: #018ffb;
  }

  .pagination {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .pagination-btn {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 6px 16px;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    background-color: #fff;
    color: #333;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s;
  }

  .pagination-btn:hover:not(:disabled) {
    background-color: #018ffb;
    color: #fff;
    border-color: #018ffb;
  }

  .pagination-btn:disabled {
    background-color: #f5f5f5;
    cursor: not-allowed;
    color: #999;
    border-color: #e8e8e8;
  }

  .arrow-icon {
    font-size: 12px;
    line-height: 1;
  }

  .page-info {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 6px 12px;
    background-color: #f5f5f5;
    border-radius: 4px;
    font-size: 14px;
  }

  .current-page {
    color: #018ffb;
    font-weight: 500;
  }

  .separator {
    color: #999;
  }

  .total-pages {
    color: #666;
  }

  .pagination-info {
    color: #666;
    font-size: 14px;
  }

  .total-count {
    color: #666;
    font-size: 14px;
  }

  .empty-data-message {
    padding: 40px 0;
    text-align: center;
  }

  .empty-data-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 10px;
  }

  .empty-icon {
    font-size: 32px;
    color: #ccc;
    margin-bottom: 10px;
  }

  .empty-data-container p {
    margin: 0;
    font-size: 16px;
    color: #666;
  }

  .empty-data-tip {
    font-size: 14px;
    color: #999;
  }
</style>
