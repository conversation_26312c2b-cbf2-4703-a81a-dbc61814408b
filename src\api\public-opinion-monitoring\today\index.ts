import { defHttp } from '/@/utils/http/axios';

enum Api {
  getHotPostList = '/dorisCrawlPageContent/getHotPostList',
  getHotTodayData = '/tokenCount/getHotTodayData',
  getPublicOpinionDataSource = '/dorisCrawlPageContent/piePlanId',
  getPublicOpinionDataToday = '/dorisCrawlPageContent/getPublicOpinionDataPlanId',
  getSentimentDetailList = '/dorisCrawlPageContent/getSentimentDetailList',
  getTrendingToday = '/tokenCount/getTrendingToday',
  getUserLocationProvinceByPlanId = '/dorisCrawlPageContent/getUserLocationProvinceByPlanId',
  getCustomNameByPlanId = '/plan/getCustomNameByPlanId',
  saveWebpage = '/webpage/save',
  getWebpage = '/webpage/get',
}

/**
 * @description: 获取今日热帖列表
 * @param params 请求参数
 * @returns 热帖列表数据
 */
export function getHotPostListApi(params) {
  return defHttp.get({
    url: Api.getHotPostList,
    params,
  });
}
/**
 * @description: 获取今日热点数据
 */
export function getHotTodayDataApi(params) {
  return defHttp.get({
    url: Api.getHotTodayData,
    params,
  });
}
/**
 * @description: 获取舆情渠道饼图数据
 */
export function getPublicOpinionDataSourceApi(params) {
  return defHttp.get({
    url: Api.getPublicOpinionDataSource,
    params,
  });
}
/**
 * @description: 获取今日舆情数据
 */
export function getPublicOpinionDataTodayApi(params) {
  return defHttp.get({
    url: Api.getPublicOpinionDataToday,
    params,
  });
}

/**
 * @description: 获取情感详情列表
 * @param params 请求参数
 * @returns 情感详情列表数据
 */
export function getSentimentDetailListApi(params) {
  return defHttp.post({
    url: Api.getSentimentDetailList,
    params,
  });
}

/**
 * @description: 获取今日热门话题
 * @param params 请求参数
 * @returns 热门话题数据
 */
export function getTrendingTodayApi(params) {
  return defHttp.get({
    url: Api.getTrendingToday,
    params,
  });
}
/**
 * @description: 获取用户画像
 * @param params 请求参数
 * @returns 用户画像数据
 */
export function getUserLocationProvinceByPlanIdApi(params) {
  return defHttp.post({
    url: Api.getUserLocationProvinceByPlanId,
    params,
  });
}

/**
 * @description: 获取渠道名称
 * @param params 请求参数
 * @returns 渠道名称数据
 */
export function getCustomNameByPlanIdApi(params) {
  return defHttp.get({
    url: Api.getCustomNameByPlanId,
    params,
  });
}

/**
 * @description: 保存网页筛选条件
 * @param params 请求参数
 * @returns 保存网页筛选条件
 */
export function saveWebpageApi(params) {
  return defHttp.post({
    url: Api.saveWebpage,
    params,
  });
}

/**
 * @description: 获取网页筛选条件
 * @param params 请求参数
 * @param options 请求选项
 * @returns 获取网页筛选条件
 */
export function getWebpageApi(params, options = {}) {
  return defHttp.get({
    url: Api.getWebpage,
    params,
  }, options);
}