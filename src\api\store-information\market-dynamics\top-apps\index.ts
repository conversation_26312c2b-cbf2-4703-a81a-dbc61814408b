import { defHttp } from '/@/utils/http/axios';

enum Api {
  getAppList = '/shop/appLeaderboards/appList',
  getAllGenre = '/shop/getAllGenre',
  queryAppByName = '/appInfo/queryByName',
  getAllCountry = '/shop/getAllCountry',
}

/**
 * @description: 获取应用排行榜信息
 * @param data 请求参数
 * @returns 应用排行榜数据
 */
export function getAppListApi(data) {
  return defHttp.post({
    url: Api.getAppList,
    data,
  });
}

/**
 * @description: 获取所有游戏类型
 * @returns 游戏类型列表数据
 */
export function getAllGenreApi() {
  return defHttp.get({
    url: Api.getAllGenre,
  });
}

/**
 * @description: 根据名称查询应用
 * @param params 请求参数
 * @returns 应用查询结果
 */
export function queryAppByNameApi(params) {
  return defHttp.get({
    url: Api.queryAppByName,
    params,
  });
}

/**
 * @description: 获取所有国家类型
 * @returns 国家类型列表数据
 */
export function getAllCountryApi() {
  return defHttp.get({
    url: Api.getAllCountry,
  });
}