import { defHttp } from '/@/utils/http/axios';

enum Api {
    queryById = '/appInfo/queryById',
    getComment = '/shop/getComment',
    getCommentCount = '/shop/getCommentCount'


}

// 获取应用详情
export function queryByIdApi(params){
  return defHttp.get({
    url: Api.queryById,
    params
  })
}

// 获取评论列表
export function getCommentApi(params){
  return defHttp.get({
    url: Api.getComment,
    params
  })
}

// 获取评分
export function getCommentCountApi(params){
  return defHttp.get({
    url: Api.getCommentCount,
    params
  })
}



